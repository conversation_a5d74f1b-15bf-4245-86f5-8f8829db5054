PODS:
  - audio_service (0.0.1):
    - Flutter
    - FlutterMacOS
  - audio_session (0.0.1):
    - FlutterMacOS
  - connectivity_plus (0.0.1):
    - FlutterMacOS
  - ffmpeg_kit_flutter_new (1.0.0):
    - ffmpeg_kit_flutter_new/full-gpl (= 1.0.0)
    - FlutterMacOS
  - ffmpeg_kit_flutter_new/full-gpl (1.0.0):
    - FlutterMacOS
  - file_picker (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - flutter_plugin_stkouyu (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - media_kit_libs_macos_video (1.0.4):
    - FlutterMacOS
  - media_kit_video (0.0.1):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - screen_brightness_macos (0.1.0):
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - volume_controller (0.0.1):
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - audio_service (from `Flutter/ephemeral/.symlinks/plugins/audio_service/darwin`)
  - audio_session (from `Flutter/ephemeral/.symlinks/plugins/audio_session/macos`)
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - ffmpeg_kit_flutter_new (from `Flutter/ephemeral/.symlinks/plugins/ffmpeg_kit_flutter_new/macos`)
  - file_picker (from `Flutter/ephemeral/.symlinks/plugins/file_picker/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - flutter_plugin_stkouyu (from `Flutter/ephemeral/.symlinks/plugins/flutter_plugin_stkouyu/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - media_kit_libs_macos_video (from `Flutter/ephemeral/.symlinks/plugins/media_kit_libs_macos_video/macos`)
  - media_kit_video (from `Flutter/ephemeral/.symlinks/plugins/media_kit_video/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - screen_brightness_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos`)
  - sign_in_with_apple (from `Flutter/ephemeral/.symlinks/plugins/sign_in_with_apple/macos`)
  - sqflite_darwin (from `Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin`)
  - video_player_avfoundation (from `Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin`)
  - volume_controller (from `Flutter/ephemeral/.symlinks/plugins/volume_controller/macos`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)
  - webview_flutter_wkwebview (from `Flutter/ephemeral/.symlinks/plugins/webview_flutter_wkwebview/darwin`)

EXTERNAL SOURCES:
  audio_service:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_service/darwin
  audio_session:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_session/macos
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  ffmpeg_kit_flutter_new:
    :path: Flutter/ephemeral/.symlinks/plugins/ffmpeg_kit_flutter_new/macos
  file_picker:
    :path: Flutter/ephemeral/.symlinks/plugins/file_picker/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  flutter_plugin_stkouyu:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_plugin_stkouyu/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  media_kit_libs_macos_video:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_libs_macos_video/macos
  media_kit_video:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_video/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  screen_brightness_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos
  sign_in_with_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/sign_in_with_apple/macos
  sqflite_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin
  video_player_avfoundation:
    :path: Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin
  volume_controller:
    :path: Flutter/ephemeral/.symlinks/plugins/volume_controller/macos
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos
  webview_flutter_wkwebview:
    :path: Flutter/ephemeral/.symlinks/plugins/webview_flutter_wkwebview/darwin

SPEC CHECKSUMS:
  audio_service: aa99a6ba2ae7565996015322b0bb024e1d25c6fd
  audio_session: eaca2512cf2b39212d724f35d11f46180ad3a33e
  connectivity_plus: 4adf20a405e25b42b9c9f87feff8f4b6fde18a4e
  ffmpeg_kit_flutter_new: 232d8f11b0946313b329b1b444aaf41c8bf8dccc
  file_picker: 7584aae6fa07a041af2b36a2655122d42f578c1a
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  flutter_plugin_stkouyu: 05325e7ca7e822f71a82d122db30d676ca2bfbc0
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  media_kit_libs_macos_video: 85a23e549b5f480e72cae3e5634b5514bc692f65
  media_kit_video: fa6564e3799a0a28bff39442334817088b7ca758
  package_info_plus: f0052d280d17aa382b932f399edf32507174e870
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  screen_brightness_macos: 2a3ee243f8051c340381e8e51bcedced8360f421
  sign_in_with_apple: 6673c03c9e3643f6c8d33601943fbfa9ae99f94e
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  volume_controller: 5c068e6d085c80dadd33fc2c918d2114b775b3dd
  wakelock_plus: 21ddc249ac4b8d018838dbdabd65c5976c308497
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: b4f78b3f5549d36842f6c0d6781116692dc6760f

COCOAPODS: 1.16.2
