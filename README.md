# lsenglish

A new Flutter project.


沙盒密码 Wk_2#9gtw#69mQ_
Yongjun0118*


```flutter
 fvm flutter pub add pretty_dio_logger  dio_cache_interceptor intl fluttertoast retrofit animations image_picker extended_image path permission_handler file_picker path_provider media_kit media_kit_video media_kit_libs_video flutter_sound volume_controller screen_brightness path_provider_foundation webview_flutter webview_flutter_android webview_flutter_wkwebview archive open_filex tuple focus_detector gap audio_service receive_sharing_intent sign_in_with_apple flutter_slidable flutter_svg package_info_plus audio_session logger diff_match_patch crypto mime cupertino_icons permission_handler_platform_interface connectivity_plus scroll_to_index fl_chart convert persistent_bottom_nav_bar super_tooltip syncfusion_flutter_charts charset encrypt circular_gradient_progress flutter_cache_manager flutter_chat_ui flutter_chat_types uuid back_button_interceptor get_storage shared_preferences ffmpeg_kit_flutter_new flutter_smart_dialog flutter_plugin_stkouyu
```


```bash
➜  lsenglish git:(main) ✗ flutter config --jdk-dir="/opt/homebrew/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home/bin/java"
Setting "jdk-dir" value to "/opt/homebrew/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home/bin/java".

You may need to restart any open editors for them to read new settings.
```


下载视频
提取音频
3
转录音频
4
自动分割句子
5
按意义分割句子
6
总结内容
7
翻译文本
8
分割字幕
9
对齐时间戳
10
合成视频
11
后续处理



https://github.com/azamor-luccas/audio_service/blob/minor/audio_service/example/lib/example_video_player.dart