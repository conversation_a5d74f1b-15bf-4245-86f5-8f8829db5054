import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/audio_recorder_utils.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';

/// 火山引擎流式语音识别工具类
/// 支持两种模式：
/// 1. 大模型流式语音识别（优化版本）：wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_async
/// 2. 普通流式语音识别：wss://openspeech.bytedance.com/api/v2/asr
/// 语音识别模式枚举
enum SpeechRecognitionMode {
  bigModel, // 大模型流式语音识别
  standard, // 普通流式语音识别
}

class VolcengineSpeechRecognition {
  // API端点配置
  static const String _baseUrl = 'wss://openspeech.bytedance.com';
  static const String _bigModelApiPath = '/api/v3/sauc/bigmodel_async';
  static const String _standardApiPath = '/api/v2/asr';

  // 火山引擎API鉴权参数（必需）
  static const String _appId = '4690637193'; // X-Api-App-Key: 使用火山引擎控制台获取的APP ID
  static const String _accessToken = '-f2KhqVBkSnVoikP45roVAeJtSnIJY2S'; // X-Api-Access-Key: 使用火山引擎控制台获取的Access Token
  static const String _resourceId = 'volc.bigasr.sauc.duration'; // X-Api-Resource-Id: 小时版资源ID（根据官方demo）

  // 状态管理
  final RxBool _isInitialized = false.obs;
  final RxBool _isRecording = false.obs;
  final RxBool _isStreaming = false.obs;
  final RxBool _isWebSocketReady = false.obs; // 新增：WebSocket连接就绪状态

  // 结果通知
  final RxString _intermediateResult = ''.obs;
  final RxString _finalResult = ''.obs;
  final RxString _errorMessage = ''.obs;

  // WebSocket连接
  WebSocketChannel? _webSocketChannel;
  StreamSubscription? _webSocketSubscription;

  // 录音相关
  final AudioRecorderUtils _audioRecorder = AudioRecorderUtils();
  StreamSubscription? _recordingDataSubscription;

  // 当前识别模式
  SpeechRecognitionMode _currentMode = SpeechRecognitionMode.bigModel;

  // 序列号管理（根据官方demo）
  int _sequenceNumber = 1;

  // 大模型配置参数 - 优化为更快的响应速度
  final Map<String, dynamic> _bigModelParams = {
    'model_name': 'bigmodel', // 模型名称
    'model_version': '400', // 使用400模型效果
    'enable_itn': true, // 是否启用ITN（数字文本标准化）
    'enable_punc': true, // 是否启用标点符号
    'enable_ddc': false, // 是否启用顺滑
    'show_utterances': true, // 输出语音停顿、分句、分词信息
    'result_type': 'full', // 全量返回
    'vad_segment_duration': 2000, // 减少到2000ms，提高响应速度
    'end_window_size': 600, // 减少到600ms，更快判停
    'force_to_speech_time': 300, // 减少到800ms，更快开始识别
  };

  // 普通流式语音识别配置参数
  final Map<String, dynamic> _standardParams = {
    'reqid': '', // 请求标识，将在运行时生成
    'nbest': 1, // 识别结果候选数目
    'workflow': 'audio_in,resample,partition,vad,fe,decode,itn,nlu_punctuate', // 工作流
    'show_language': false, // 是否显示语言信息
    'show_utterances': true, // 输出语音停顿、分句、分词信息
    'result_type': 'full', // 返回结果类型
    'sequence': 1, // 请求序号
  };

  // Getter方法
  bool get isInitialized => _isInitialized.value;
  bool get isRecording => _isRecording.value;
  bool get isStreaming => _isStreaming.value;
  String get intermediateResult => _intermediateResult.value;
  String get finalResult => _finalResult.value;
  String get errorMessage => _errorMessage.value;

  // 状态监听器
  RxBool get isInitializedObs => _isInitialized;
  RxBool get isRecordingObs => _isRecording;
  RxBool get isStreamingObs => _isStreaming;
  RxString get intermediateResultObs => _intermediateResult;
  RxString get finalResultObs => _finalResult;
  RxString get errorMessageObs => _errorMessage;

  VolcengineSpeechRecognition();

  /// 设置语音识别模式
  void setMode(SpeechRecognitionMode mode) {
    _currentMode = mode;
    logger('[VolcengineSpeechRecognition] 设置识别模式: ${mode.name}');
  }

  /// 获取当前识别模式
  SpeechRecognitionMode get currentMode => _currentMode;

  /// 初始化语音识别服务
  Future<bool> initialize() async {
    try {
      // 初始化录音器
      await _initializeRecorder();

      _isInitialized.value = true;
      logger('[VolcengineSpeechRecognition] 初始化成功');
      return true;
    } catch (e) {
      _errorMessage.value = '初始化失败: $e';
      logger('[VolcengineSpeechRecognition] 初始化失败: $e');
      return false;
    }
  }

  /// 初始化录音器
  Future<void> _initializeRecorder() async {
    try {
      await _audioRecorder.init();
      logger('[VolcengineSpeechRecognition] 录音器初始化成功');
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 录音器初始化失败: $e');
      throw Exception('录音器初始化失败: $e');
    }
  }

  /// 开始实时录音和流式语音识别
  Future<bool> startRealTimeRecording() async {
    if (!_isInitialized.value) {
      _errorMessage.value = '请先初始化语音识别服务';
      return false;
    }

    if (!_audioRecorder.isInitialized) {
      _errorMessage.value = '录音器未初始化';
      return false;
    }

    // 检查录音权限
    final hasPermission = await _checkRecordingPermission();
    if (!hasPermission) {
      _errorMessage.value = '没有录音权限';
      return false;
    }

    try {
      _isRecording.value = true;
      _isStreaming.value = true;
      _intermediateResult.value = '';
      _finalResult.value = '';
      _errorMessage.value = '';

      // 并行执行：同时开始录音和建立WebSocket连接
      await Future.wait([
        _startRecordingAndStreaming(), // 立即开始录音
        _establishWebSocketConnection(), // 同时建立连接
      ]);

      return true;
    } catch (e) {
      _errorMessage.value = '开始实时录音失败: $e';
      _isRecording.value = false;
      _isStreaming.value = false;
      logger('[VolcengineSpeechRecognition] 开始实时录音失败: $e');
      return false;
    }
  }

  /// 检查录音权限
  Future<bool> _checkRecordingPermission() async {
    final status = await Permission.microphone.status;
    if (status.isGranted) {
      return true;
    }

    if (status.isDenied) {
      final result = await Permission.microphone.request();
      return result.isGranted;
    }

    return false;
  }

  /// 建立WebSocket连接的独立方法
  Future<void> _establishWebSocketConnection() async {
    try {
      // 构建WebSocket连接URL
      final wsUrl = _buildWebSocketUrl(_currentMode);

      // 建立WebSocket连接（带鉴权头部）
      await _connectWebSocketWithAuth(wsUrl, _currentMode);

      // 发送full client request
      await _sendFullClientRequest();

      logger('[VolcengineSpeechRecognition] WebSocket连接建立完成');
    } catch (e) {
      logger('[VolcengineSpeechRecognition] WebSocket连接建立失败: $e');
      // 连接失败时，录音仍然可以继续，只是无法发送数据
      _isWebSocketReady.value = false;
    }
  }

  /// 开始录音并实时流式传输
  Future<void> _startRecordingAndStreaming() async {
    try {
      // 使用录音工具类开始录音
      await _audioRecorder.startRecord(
        (audioData) => _handleRecordingData(audioData),
        onTimeout: () {
          logger('[VolcengineSpeechRecognition] 录音超时，自动停止');
          _isRecording.value = false;
          _isStreaming.value = false;
        },
        maxDuration: const Duration(seconds: 60),
      );

      logger('[VolcengineSpeechRecognition] 开始实时录音流');
    } catch (e) {
      _errorMessage.value = '启动录音流失败: $e';
      _isRecording.value = false;
      _isStreaming.value = false;
      _stopRecording();

      throw Exception('启动录音流失败: $e');
    }
  }

  // 音频数据缓冲，用于优化分包大小
  final List<Uint8List> _audioBuffer = [];
  static const int _targetAudioSize = 6400; // 200ms音频数据大小 (16000 * 2 * 0.2)
  
  // 预连接音频数据缓冲区，用于在WebSocket连接建立前缓存音频数据
  final List<Uint8List> _preConnectionBuffer = [];
  static const int _maxPreConnectionBufferSize = 32000; // 1秒的音频数据大小

  /// 处理录音数据
  void _handleRecordingData(Uint8List data) {
    try {
      // 如果WebSocket还没就绪，使用预连接缓冲区
      if (!_isWebSocketReady.value) {
        _preConnectionBuffer.add(data);
        
        // 限制预连接缓冲区大小，避免内存占用过大
        int totalPreBufferSize = 0;
        for (final chunk in _preConnectionBuffer) {
          totalPreBufferSize += chunk.length;
        }
        
        if (totalPreBufferSize > _maxPreConnectionBufferSize) {
          // 移除最旧的数据，保持缓冲区大小
          while (totalPreBufferSize > _maxPreConnectionBufferSize && _preConnectionBuffer.isNotEmpty) {
            final removed = _preConnectionBuffer.removeAt(0);
            totalPreBufferSize -= removed.length;
          }
        }
        
        logger('[VolcengineSpeechRecognition] WebSocket未就绪，音频数据已缓存到预连接缓冲区，当前大小: ${_preConnectionBuffer.length} 块');
        return;
      }

      // WebSocket已就绪，正常处理音频数据
      // 将音频数据添加到缓冲区
      _audioBuffer.add(data);

      // 计算当前缓冲区总大小
      int totalSize = 0;
      for (final chunk in _audioBuffer) {
        totalSize += chunk.length;
      }

      // 当缓冲区达到目标大小时发送
      if (totalSize >= _targetAudioSize) {
        // 合并音频数据
        final combinedData = Uint8List(totalSize);
        int offset = 0;
        for (final chunk in _audioBuffer) {
          combinedData.setRange(offset, offset + chunk.length, chunk);
          offset += chunk.length;
        }

        // 发送合并后的音频数据
        _sendAudioOnlyRequest(combinedData);

        // 清空缓冲区
        _audioBuffer.clear();

        logger('[VolcengineSpeechRecognition] 发送优化音频包，大小: $totalSize bytes (约${(totalSize / 32000 * 1000).round()}ms)');
      }
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 处理录音数据失败: $e');

      _errorMessage.value = '处理录音数据失败: $e';
      _isRecording.value = false;
      _isStreaming.value = false;
      _stopRecording();
    }
  }

  /// 构建WebSocket连接URL
  String _buildWebSocketUrl(SpeechRecognitionMode mode) {
    final apiPath = mode == SpeechRecognitionMode.bigModel ? _bigModelApiPath : _standardApiPath;
    return '$_baseUrl$apiPath';
  }

  /// 生成请求ID（根据官方demo，应该是Request-Id，不是Connect-Id）
  String _generateRequestId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// 生成用户ID
  Future<String> _generateUid() async {
    try {
      final deviceInfo = await DeviceInfoPlugin().deviceInfo;
      final deviceId = deviceInfo.data['id'] as String?;
      
      if (deviceId != null && deviceId.isNotEmpty) {
        logger('[VolcengineSpeechRecognition] 使用设备ID作为UID: $deviceId');
        return deviceId;
      }
      
      // 如果设备ID为空，生成一个基于时间戳的UID
      final fallbackUid = 'flutter_${DateTime.now().millisecondsSinceEpoch}';
      logger('[VolcengineSpeechRecognition] 设备ID为空，使用fallback UID: $fallbackUid');
      return fallbackUid;
    } catch (e) {
      // 如果获取设备信息失败，使用默认UID
      const defaultUid = '388808088185088';
      logger('[VolcengineSpeechRecognition] 获取设备信息失败: $e，使用默认UID: $defaultUid');
      return defaultUid;
    }
  }

  /// Gzip压缩
  Uint8List _gzipCompress(Uint8List data) {
    try {
      final compressed = GZipCodec().encode(data);
      return Uint8List.fromList(compressed);
    } catch (e) {
      logger('[VolcengineSpeechRecognition] Gzip压缩失败: $e');
      return data; // 压缩失败时返回原始数据
    }
  }

  /// Gzip解压
  Uint8List _gzipDecompress(Uint8List data) {
    try {
      final decompressed = GZipCodec().decode(data);
      return Uint8List.fromList(decompressed);
    } catch (e) {
      logger('[VolcengineSpeechRecognition] Gzip解压失败: $e');
      return data; // 解压失败时返回原始数据
    }
  }

  /// 建立带鉴权头部的WebSocket连接
  Future<void> _connectWebSocketWithAuth(String wsUrl, SpeechRecognitionMode mode) async {
    try {
      // 关闭现有连接
      await _disconnectWebSocket();

      // 重置序列号 - 每次新连接时重置为1
      _sequenceNumber = 1;
      logger('[VolcengineSpeechRecognition] 序列号已重置为: $_sequenceNumber');

      // 生成请求ID
      final requestId = _generateRequestId();

      // 创建自定义的WebSocket连接，支持HTTP头部
      final uri = Uri.parse(wsUrl);

      Map<String, String> headers = {};

      if (mode == SpeechRecognitionMode.bigModel) {
        // 大模型模式使用X-Api-*头部
        headers = {
          'X-Api-App-Key': _appId,
          'X-Api-Access-Key': _accessToken,
          'X-Api-Resource-Id': _resourceId,
          'X-Api-Request-Id': requestId,
        };
        logger('[VolcengineSpeechRecognition] 大模型模式：使用X-Api-*头部鉴权');
      } else {
        // 普通流式模式使用Authorization头部
        headers = {
          'Authorization': 'Bearer; $_accessToken',
        };
        logger('[VolcengineSpeechRecognition] 普通流式模式：使用Authorization头部鉴权');
        logger('[VolcengineSpeechRecognition] 普通流式模式鉴权头部: Authorization=Bearer; $_accessToken');
      }

      // 使用IOWebSocketChannel来避免URL解析问题
      try {
        _webSocketChannel = IOWebSocketChannel.connect(
          wsUrl,
          headers: headers,
        );
        logger('[VolcengineSpeechRecognition] 使用IOWebSocketChannel连接，模式: ${mode.name} _webSocketChannel=$_webSocketChannel  sink=${_webSocketChannel?.sink}');
      } catch (e) {
        logger('[VolcengineSpeechRecognition] IOWebSocketChannel连接失败，回退到基本连接: $e');
        _webSocketChannel = WebSocketChannel.connect(uri);
      }

      // 监听消息
      _webSocketSubscription = _webSocketChannel!.stream.listen(
        (data) => _handleWebSocketMessage(data),
        onError: (error) => _handleWebSocketError(error),
        onDone: () => _handleWebSocketDone(),
      );

      logger('[VolcengineSpeechRecognition] WebSocket连接已建立: $wsUrl (模式: ${mode.name})');
      logger('[VolcengineSpeechRecognition] 鉴权参数: ${headers.toString()}');

      // 等待连接建立并检查状态
      // await Future.delayed(const Duration(milliseconds: 2000));

      // 检查连接状态
      if (_webSocketChannel == null || _webSocketChannel?.sink == null) {
        throw Exception('WebSocket连接建立失败');
      }

      // 额外检查：确保连接真正建立
      try {
        // 尝试发送一个测试消息来验证连接
        if (mode == SpeechRecognitionMode.bigModel) {
          // 大模型模式不需要额外检查
          logger('[VolcengineSpeechRecognition] 大模型模式连接检查完成');
          _isWebSocketReady.value = true;
          // 大模型模式连接成功后，补发预连接的音频数据
          _sendPreConnectionBufferData();
        } else {
          // 普通流式模式需要确保连接真正建立
          logger('[VolcengineSpeechRecognition] 普通流式模式连接检查完成');
          // 普通流式模式需要等待服务器响应后才设置就绪状态
          // 这里暂时设置为true，在收到服务器响应后会重新设置
          _isWebSocketReady.value = true;
          // 普通流式模式连接成功后，补发预连接的音频数据
          _sendPreConnectionBufferData();
        }
      } catch (e) {
        logger('[VolcengineSpeechRecognition] 连接验证失败: $e');
        throw Exception('WebSocket连接验证失败: $e');
      }

      logger('[VolcengineSpeechRecognition] WebSocket连接状态检查完成');
    } catch (e) {
      _errorMessage.value = '建立WebSocket连接失败: $e';
      _isRecording.value = false;
      _isStreaming.value = false;
      _stopRecording();

      throw Exception('建立WebSocket连接失败: $e');
    }
  }

  /// 发送full client request（根据文档要求）
  Future<void> _sendFullClientRequest() async {
    if (_webSocketChannel == null) {
      throw Exception('WebSocket连接未建立');
    }

    try {
      Map<String, dynamic> payload;

      if (_currentMode == SpeechRecognitionMode.bigModel) {
        // 大模型模式的payload
        payload = {
          'user': {
            'uid': await _generateUid(),
            'did': (await DeviceInfoPlugin().deviceInfo).data['name'] ?? 'flutter_device',
            'platform': Platform.operatingSystem,
            'sdk_version': '1.0.0',
            'app_version': '1.0.0',
          },
          'audio': {
            'format': 'pcm',
            'codec': 'raw',
            'rate': 16000,
            'bits': 16,
            'channel': 1,
            'language': 'zh-CN',
          },
          'request': _bigModelParams,
        };
      } else {
        // 普通流式模式的payload
        final reqid = DateTime.now().millisecondsSinceEpoch.toString();
        final standardParams = Map<String, dynamic>.from(_standardParams);
        standardParams['reqid'] = reqid;

        payload = {
          'app': {
            'appid': _appId,
            'cluster': 'volcengine_streaming',
            'token': _accessToken,
          },
          'user': {
            'uid': await _generateUid(),
          },
          'audio': {
            'format': 'raw', // 根据文档修改为raw
            'codec': 'raw', // 根据文档保持raw
            'rate': 16000,
            'bits': 16,
            'channel': 1,
            'language': 'zh-CN',
          },
          'request': standardParams,
        };
      }

      // 构建二进制消息
      Uint8List message;
      if (_currentMode == SpeechRecognitionMode.bigModel) {
        // 大模型模式：4字节header + sequence + payload size + payload
        message = _buildBinaryMessage(
          messageType: 0x01, // full client request
          messageFlags: 0x01, // 有sequence
          serializationMethod: 0x01, // JSON格式
          compression: 0x01, // Gzip压缩
          payload: json.encode(payload),
          sequence: _sequenceNumber,
        );
        _sequenceNumber++;
      } else {
        // 普通流式模式：4字节header + payload size + payload
        message = _buildStandardBinaryMessage(
          messageType: 0x01, // full client request
          messageFlags: 0x00, // 无sequence
          serializationMethod: 0x01, // JSON格式
          compression: 0x01, // Gzip压缩
          payload: json.encode(payload),
        );
      }

      _webSocketChannel!.sink.add(message);
      logger('[VolcengineSpeechRecognition] full client request已发送 (模式: ${_currentMode.name})');

              // 在普通流式模式下，需要等待服务器响应后才能开始录音
        if (_currentMode == SpeechRecognitionMode.standard) {
          logger('[VolcengineSpeechRecognition] 普通流式模式：等待服务器响应...');
          logger('[VolcengineSpeechRecognition] 已发送的payload内容: ${json.encode(payload)}');
          // 暂时不设置WebSocket就绪状态，等待服务器响应
          _isWebSocketReady.value = false;
        }
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 发送full client request失败: $e');
      throw Exception('发送full client request失败: $e');
    }
  }

  /// 发送剩余的缓冲音频数据
  void _sendRemainingAudioData() {
    // 先发送预连接缓冲区的数据
    _sendPreConnectionBufferData();
    
    if (_audioBuffer.isEmpty) return;

    try {
      // 计算当前缓冲区总大小
      int totalSize = 0;
      for (final chunk in _audioBuffer) {
        totalSize += chunk.length;
      }

      if (totalSize > 0) {
        // 合并音频数据
        final combinedData = Uint8List(totalSize);
        int offset = 0;
        for (final chunk in _audioBuffer) {
          combinedData.setRange(offset, offset + chunk.length, chunk);
          offset += chunk.length;
        }

        // 发送合并后的音频数据
        _sendAudioOnlyRequest(combinedData);

        logger('[VolcengineSpeechRecognition] 发送剩余缓冲音频数据，大小: $totalSize bytes');
      }

      // 清空缓冲区
      _audioBuffer.clear();
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 发送剩余缓冲音频数据失败: $e');
    }
  }

  /// 发送预连接缓冲区的音频数据
  void _sendPreConnectionBufferData() {
    if (_preConnectionBuffer.isEmpty) return;

    try {
      // 计算预连接缓冲区总大小
      int totalSize = 0;
      for (final chunk in _preConnectionBuffer) {
        totalSize += chunk.length;
      }

      if (totalSize > 0) {
        // 合并预连接缓冲区的音频数据
        final combinedData = Uint8List(totalSize);
        int offset = 0;
        for (final chunk in _preConnectionBuffer) {
          combinedData.setRange(offset, offset + chunk.length, chunk);
          offset += chunk.length;
        }

        // 发送合并后的音频数据
        _sendAudioOnlyRequest(combinedData);

        logger('[VolcengineSpeechRecognition] 预连接音频数据已发送，大小: $totalSize bytes (约${(totalSize / 32000 * 1000).round()}ms)');
      }

      // 清空预连接缓冲区
      _preConnectionBuffer.clear();
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 发送预连接音频数据失败: $e');
    }
  }

  /// 发送audio only request（根据文档要求）
  void _sendAudioOnlyRequest(Uint8List audioData) {
    if (_webSocketChannel == null || _webSocketChannel?.sink == null) {
      logger('[VolcengineSpeechRecognition] WebSocket连接未建立，跳过音频块发送 _webSocketChannel=$_webSocketChannel  sink=${_webSocketChannel?.sink}');
      return;
    }

    // 检查WebSocket就绪状态
    if (!_isWebSocketReady.value) {
      logger('[VolcengineSpeechRecognition] WebSocket未就绪，跳过音频块发送');
      return;
    }

    // 检查连接状态
    try {
      // 尝试检查连接是否真正可用
      if (!_isStreaming.value) {
        logger('[VolcengineSpeechRecognition] 流式状态为false，跳过音频块发送');
        return;
      }
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 连接状态检查失败: $e');
      return;
    }

    try {
      Uint8List message;

      if (_currentMode == SpeechRecognitionMode.bigModel) {
        // 大模型模式：4字节header + sequence + payload size + payload
        message = _buildBinaryMessage(
          messageType: 0x02, // audio only request
          messageFlags: 0x01, // 有sequence
          serializationMethod: 0x00, // 无序列化（raw bytes）
          compression: 0x01, // Gzip压缩
          payload: audioData,
          sequence: _sequenceNumber,
        );
        _sequenceNumber++;
      } else {
        // 普通流式模式：4字节header + payload size + payload
        message = _buildStandardBinaryMessage(
          messageType: 0x02, // audio only request
          messageFlags: 0x00, // 无sequence
          serializationMethod: 0x00, // 无序列化（raw bytes）
          compression: 0x01, // Gzip压缩
          payload: audioData,
        );
      }

      _webSocketChannel!.sink.add(message);
      logger('[VolcengineSpeechRecognition] audio only request已发送, 大小: ${audioData.length} (模式: ${_currentMode.name})');
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 发送audio only request失败: $e');

      _errorMessage.value = '发送音频数据失败: $e';
      _isRecording.value = false;
      _isStreaming.value = false;
      _stopRecording();
    }
  }

  /// 构建二进制消息（4字节header + sequence + payload size + payload）
  Uint8List _buildBinaryMessage({
    required int messageType,
    required int messageFlags,
    required int serializationMethod,
    required int compression,
    required dynamic payload,
    int sequence = 1,
  }) {
    // 4字节header
    final header = Uint8List(4);

    // Protocol version (4 bits) + Header size (4 bits)
    header[0] = (0x01 << 4) | 0x01; // version 1, header size 1

    // Message type (4 bits) + Message type specific flags (4 bits)
    header[1] = (messageType << 4) | messageFlags;

    // Message serialization method (4 bits) + Message compression (4 bits)
    header[2] = (serializationMethod << 4) | compression;

    // Reserved (8 bits)
    header[3] = 0x00;

    // 处理payload
    Uint8List payloadBytes;
    if (payload is String) {
      payloadBytes = Uint8List.fromList(utf8.encode(payload));
    } else if (payload is Uint8List) {
      payloadBytes = payload;
    } else {
      payloadBytes = Uint8List(0);
    }

    // 根据压缩类型处理payload
    if (compression == 0x01) {
      // Gzip压缩
      payloadBytes = _gzipCompress(payloadBytes);
    }

    // 4字节sequence（大端序）
    final sequenceBytes = Uint8List(4);
    sequenceBytes[0] = (sequence >> 24) & 0xFF;
    sequenceBytes[1] = (sequence >> 16) & 0xFF;
    sequenceBytes[2] = (sequence >> 8) & 0xFF;
    sequenceBytes[3] = sequence & 0xFF;

    // 4字节payload size（大端序）
    final payloadSize = Uint8List(4);
    final size = payloadBytes.length;
    payloadSize[0] = (size >> 24) & 0xFF;
    payloadSize[1] = (size >> 16) & 0xFF;
    payloadSize[2] = (size >> 8) & 0xFF;
    payloadSize[3] = size & 0xFF;

    // 组合完整的消息：header(4) + sequence(4) + payload_size(4) + payload
    final message = Uint8List(4 + 4 + 4 + payloadBytes.length);
    message.setRange(0, 4, header);
    message.setRange(4, 8, sequenceBytes);
    message.setRange(8, 12, payloadSize);
    message.setRange(12, 12 + payloadBytes.length, payloadBytes);

    return message;
  }

  /// 构建普通流式语音识别的二进制消息（4字节header + payload size + payload）
  Uint8List _buildStandardBinaryMessage({
    required int messageType,
    required int messageFlags,
    required int serializationMethod,
    required int compression,
    required dynamic payload,
  }) {
    // 4字节header
    final header = Uint8List(4);

    // Protocol version (4 bits) + Header size (4 bits)
    header[0] = (0x01 << 4) | 0x01; // version 1, header size 1

    // Message type (4 bits) + Message type specific flags (4 bits)
    header[1] = (messageType << 4) | messageFlags;

    // Message serialization method (4 bits) + Message compression (4 bits)
    header[2] = (serializationMethod << 4) | compression;

    // Reserved (8 bits)
    header[3] = 0x00;

    // 处理payload
    Uint8List payloadBytes;
    if (payload is String) {
      payloadBytes = Uint8List.fromList(utf8.encode(payload));
    } else if (payload is Uint8List) {
      payloadBytes = payload;
    } else {
      payloadBytes = Uint8List(0);
    }

    // 根据压缩类型处理payload
    if (compression == 0x01) {
      // Gzip压缩
      payloadBytes = _gzipCompress(payloadBytes);
    }

    // 4字节payload size（大端序）
    final payloadSize = Uint8List(4);
    final size = payloadBytes.length;
    payloadSize[0] = (size >> 24) & 0xFF;
    payloadSize[1] = (size >> 16) & 0xFF;
    payloadSize[2] = (size >> 8) & 0xFF;
    payloadSize[3] = size & 0xFF;

    // 组合完整的消息：header(4) + payload_size(4) + payload
    final message = Uint8List(4 + 4 + payloadBytes.length);
    message.setRange(0, 4, header);
    message.setRange(4, 8, payloadSize);
    message.setRange(8, 8 + payloadBytes.length, payloadBytes);

    return message;
  }

  /// 处理WebSocket消息
  void _handleWebSocketMessage(dynamic data) {
    try {
      if (data is Uint8List) {
        // 处理二进制消息
        _processBinaryMessage(data);
      } else if (data is String) {
        // 处理文本消息（备用）
        final jsonData = json.decode(data);
        _processRecognitionResult(jsonData);
      } else {
        logger('[VolcengineSpeechRecognition] 收到未知类型消息: $data');
      }
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 处理WebSocket消息失败: $e');
      _errorMessage.value = '处理WebSocket消息失败: $e';
    }
  }

  /// 处理二进制消息
  void _processBinaryMessage(Uint8List data) {
    try {
      if (data.length < 8) {
        // Header(4) + PayloadSize(4) = 8 bytes minimum
        logger('[VolcengineSpeechRecognition] 消息长度不足: ${data.length}');
        return;
      }

      // 解析header (4 bytes)
      final messageType = (data[1] >> 4) & 0x0F;
      final messageFlags = data[1] & 0x0F;
      final serializationMethod = (data[2] >> 4) & 0x0F;
      final compression = data[2] & 0x0F;

      int payloadSize;
      int payloadOffset;

      if (_currentMode == SpeechRecognitionMode.bigModel) {
        // 大模型模式：Header(4) + Sequence(4) + PayloadSize(4) + Payload
        if (data.length < 12) {
          logger('[VolcengineSpeechRecognition] 大模型模式消息长度不足: ${data.length}');
          return;
        }

        final sequence = (data[4] << 24) | (data[5] << 16) | (data[6] << 8) | data[7];
        payloadSize = (data[8] << 24) | (data[9] << 16) | (data[10] << 8) | data[11];
        payloadOffset = 12;

        logger('[VolcengineSpeechRecognition] 收到大模型消息: type=$messageType, flags=$messageFlags, sequence=$sequence, size=$payloadSize');
      } else {
        // 普通流式模式：Header(4) + PayloadSize(4) + Payload
        payloadSize = (data[4] << 24) | (data[5] << 16) | (data[6] << 8) | data[7];
        payloadOffset = 8;

        logger('[VolcengineSpeechRecognition] 收到普通流式消息: type=$messageType, flags=$messageFlags, size=$payloadSize');
      }

      // 处理不同类型的消息
      if (messageType == 0x09) {
        // full server response
        logger('[VolcengineSpeechRecognition] 收到Full Server Response，序列化方法: $serializationMethod, 压缩: $compression');
        if (data.length >= payloadOffset + payloadSize) {
          final payload = data.sublist(payloadOffset, payloadOffset + payloadSize);
          logger('[VolcengineSpeechRecognition] 提取payload，大小: ${payload.length}');
          _processServerResponse(payload, serializationMethod, compression);

          // 在普通流式模式下，收到第一个服务器响应后设置WebSocket就绪状态
          if (_currentMode == SpeechRecognitionMode.standard && !_isWebSocketReady.value) {
            _isWebSocketReady.value = true;
            logger('[VolcengineSpeechRecognition] 普通流式模式WebSocket已就绪，可以开始发送音频数据');

            // 补发预连接的音频数据
            _sendPreConnectionBufferData();

            // 如果还没有开始录音，现在开始录音
            if (!_isRecording.value) {
              logger('[VolcengineSpeechRecognition] 开始启动录音...');
              startRealTimeRecording();
            }
          }
        } else {
          logger('[VolcengineSpeechRecognition] 消息长度不足，期望: ${payloadOffset + payloadSize}, 实际: ${data.length}');
        }
      } else if (messageType == 0x0F) {
        // error response
        logger('[VolcengineSpeechRecognition] 收到错误响应');
        _handleServerError(data);
      } else {
        logger('[VolcengineSpeechRecognition] 收到未知消息类型: $messageType');
      }
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 处理二进制消息失败: $e');
    }
  }

  /// 处理服务器响应
  void _processServerResponse(Uint8List payload, int serializationMethod, int compression) {
    try {
      Uint8List processedPayload = payload;

      if (compression == 0x01) {
        // Gzip压缩，需要解压
        processedPayload = _gzipDecompress(payload);
      }

      final jsonString = utf8.decode(processedPayload);
      final jsonData = json.decode(jsonString);
      _processRecognitionResult(jsonData);
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 处理服务器响应失败: $e');
    }
  }

  /// 处理服务器错误
  void _handleServerError(Uint8List data) {
    try {
      // 根据文档，错误消息格式：Header(4) + Error message code(4) + Error message size(4) + Error message
      if (data.length >= 12) {
        // 解析错误码 (4字节，大端序)
        final errorCode = (data[4] << 24) | (data[5] << 16) | (data[6] << 8) | data[7];

        // 解析错误信息大小 (4字节，大端序)
        final errorSize = (data[8] << 24) | (data[9] << 16) | (data[10] << 8) | data[11];

        logger('[VolcengineSpeechRecognition] 错误码: $errorCode, 错误信息大小: $errorSize');

        if (data.length >= 12 + errorSize) {
          final errorMessage = utf8.decode(data.sublist(12, 12 + errorSize));
          logger('[VolcengineSpeechRecognition] 服务器错误: $errorCode - $errorMessage');
          _errorMessage.value = '服务器错误: $errorCode - $errorMessage';

          // 如果是序列号不匹配错误，建议重新连接
          if (errorCode == 45000000 && errorMessage.contains('mismatch sequence')) {
            logger('[VolcengineSpeechRecognition] 检测到序列号不匹配，建议重新连接');
          }
        } else {
          logger('[VolcengineSpeechRecognition] 错误消息长度不足，期望: ${12 + errorSize}, 实际: ${data.length}');
        }
      } else {
        logger('[VolcengineSpeechRecognition] 错误消息数据长度不足: ${data.length}');
      }
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 处理服务器错误失败: $e');
    }
  }

  /// 处理识别结果
  void _processRecognitionResult(Map<String, dynamic> result) {
    try {
      logger('[VolcengineSpeechRecognition] 收到识别结果: $result');

      String text = '';
      bool isFinal = false;

      if (_currentMode == SpeechRecognitionMode.bigModel) {
        // 大模型模式返回格式：
        // {
        //   "result": {
        //     "text": "这是字节跳动， 今日头条母公司。",
        //     "utterances": [...]
        //   },
        //   "audio_info": {
        //     "duration": 3696
        //   }
        // }
        final resultData = result['result'];
        if (resultData != null) {
          text = resultData['text'] ?? '';

          if (text.isNotEmpty) {
            // 检查是否有utterances
            final utterances = resultData['utterances'] as List<dynamic>?;
            if (utterances != null && utterances.isNotEmpty) {
              // 遍历所有utterances，找到definite为true的分句
              for (final utterance in utterances) {
                final utteranceMap = utterance as Map<String, dynamic>;
                final utteranceText = utteranceMap['text'] ?? '';
                final isDefinite = utteranceMap['definite'] == true;
                
                if (isDefinite && utteranceText.isNotEmpty) {
                  // 这是一个确定的分句，应该作为最终结果
                  isFinal = true;
                  logger('[VolcengineSpeechRecognition] 发现确定分句: $utteranceText');
                  break;
                }
              }
              
              // 如果没有找到definite为true的分句，但有文本内容，可能是中间结果
              if (!isFinal) {
                logger('[VolcengineSpeechRecognition] 没有找到确定分句，当前为中间结果');
              }
            } else {
              // 如果没有utterances，但有文本内容，可能是最终结果
              isFinal = true;
              logger('[VolcengineSpeechRecognition] 没有utterances，但有文本内容，判断为最终结果');
            }
          }
        }

        // 检查audio_info
        final audioInfo = result['audio_info'];
        if (audioInfo != null) {
          final duration = audioInfo['duration'];
          logger('[VolcengineSpeechRecognition] 音频时长: ${duration}ms');
        }
      } else {
        // 普通流式模式返回格式：
        // {
        //   "reqid": "0ce870af-c0f0-4208-aae7-bd7cdf063567",
        //   "code": 1000,
        //   "message": "Success",
        //   "sequence": -1,
        //   "result": [
        //     {
        //       "text": "这是字节跳动， 今日头条母公司。",
        //       "utterances": [...]
        //     }
        //   ],
        //   "addition": {
        //     "duration": "3696",
        //     "logid": "20230606120441ECA45EBB4E6A6B036C55"
        //   }
        // }

        // 检查返回码
        final code = result['code'];
        if (code != 1000) {
          final message = result['message'] ?? '未知错误';
          logger('[VolcengineSpeechRecognition] 识别失败: $code - $message');
          _errorMessage.value = '识别失败: $code - $message';
          return;
        }

        final resultList = result['result'] as List<dynamic>?;
        if (resultList != null && resultList.isNotEmpty) {
          final firstResult = resultList.first as Map<String, dynamic>;
          text = firstResult['text'] ?? '';

          if (text.isNotEmpty) {
            // 检查是否有utterances，如果有且最后一个utterance的definite为true，则为最终结果
            final utterances = firstResult['utterances'] as List<dynamic>?;
            if (utterances != null && utterances.isNotEmpty) {
              final lastUtterance = utterances.last as Map<String, dynamic>;
              isFinal = lastUtterance['definite'] == true;
            }
          }
        }

        // 检查addition
        final addition = result['addition'];
        if (addition != null) {
          final duration = addition['duration'];
          logger('[VolcengineSpeechRecognition] 音频时长: ${duration}ms');
        }
      }

      // 处理识别结果
      if (text.isNotEmpty) {
        if (isFinal) {
          _intermediateResult.value = text;
          _finalResult.value = text;
          logger('[VolcengineSpeechRecognition] 最终识别结果: $text');
        } else {
          _intermediateResult.value = text;
          logger('[VolcengineSpeechRecognition] 中间识别结果: $text');
        }
      } else {
        logger('[VolcengineSpeechRecognition] 识别结果为空');
      }
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 处理识别结果失败: $e');
    }
  }

  /// 处理WebSocket错误
  void _handleWebSocketError(dynamic error) {
    logger('[VolcengineSpeechRecognition] WebSocket错误: $error');
    logger('[VolcengineSpeechRecognition] WebSocket错误处理开始，当前状态: _isRecording=${_isRecording.value}, _isStreaming=${_isStreaming.value}');
    
    _errorMessage.value = 'WebSocket错误: $error';
    _isRecording.value = false;
    _isStreaming.value = false;
    _isWebSocketReady.value = false;

    _stopRecording();
    _disconnectWebSocket();

    _intermediateResult.value = '';
    _finalResult.value = '';

    logger('[VolcengineSpeechRecognition] WebSocket错误处理完成，录音已自动停止，连接已断开，状态已重置');
  }

  /// 处理WebSocket连接完成
  void _handleWebSocketDone() {
    logger('[VolcengineSpeechRecognition] WebSocket连接完成事件触发，当前状态: _isRecording=${_isRecording.value}, _isStreaming=${_isStreaming.value}');
    
    _isStreaming.value = false;
    _isWebSocketReady.value = false;

    _stopRecording();
    _disconnectWebSocket();

    _intermediateResult.value = '';
    _finalResult.value = '';

    logger('[VolcengineSpeechRecognition] WebSocket连接已完成，录音已停止，连接已断开，状态已重置');
  }

  /// 断开WebSocket连接
  Future<void> _disconnectWebSocket() async {
    logger('[VolcengineSpeechRecognition] 开始断开WebSocket连接，当前状态: _webSocketChannel=${_webSocketChannel != null ? "非空" : "null"}');
    
    try {
      await _webSocketSubscription?.cancel();
      _webSocketSubscription = null;

      await _webSocketChannel?.sink.close();
      _webSocketChannel = null;

      logger('[VolcengineSpeechRecognition] WebSocket连接已断开');
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 断开WebSocket连接失败: $e');

      if (_isRecording.value) {
        _stopRecording();
      }
    }
  }

  /// 停止流式识别
  Future<void> stopStreamingRecognition() async {
    try {
      // 发送剩余的缓冲音频数据
      _sendRemainingAudioData();

      await _webSocketSubscription?.cancel();
      _webSocketSubscription = null;

      await _stopRecording();
      await _disconnectWebSocket();

      _isRecording.value = false;
      _isStreaming.value = false;

      logger('[VolcengineSpeechRecognition] 停止流式识别');
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 停止流式识别失败: $e');

      _isRecording.value = false;
      _isStreaming.value = false;
    }
  }

  /// 停止录音
  Future<void> _stopRecording() async {
    try {
      if (_audioRecorder.isRecording) {
        await _audioRecorder.stopRecord();
        logger('[VolcengineSpeechRecognition] 录音已停止');
      }

      await _recordingDataSubscription?.cancel();
      _recordingDataSubscription = null;
    } catch (e) {
      logger('[VolcengineSpeechRecognition] 停止录音失败: $e');

      _isRecording.value = false;
      _isStreaming.value = false;
    }
  }

  /// 重置状态
  void reset() {
    _intermediateResult.value = '';
    _finalResult.value = '';
    _errorMessage.value = '';
    _isRecording.value = false;
    _isStreaming.value = false;
    _isWebSocketReady.value = false;

    // 清空音频缓冲区
    _audioBuffer.clear();
    _preConnectionBuffer.clear();

    _stopRecording();
    logger('[VolcengineSpeechRecognition] 状态已重置');
  }

  /// 释放资源
  Future<void> dispose() async {
    await stopStreamingRecognition();
    await _disconnectWebSocket();

    if (_audioRecorder.isInitialized) {
      try {
        _audioRecorder.dispose();
        logger('[VolcengineSpeechRecognition] 录音器已释放');
      } catch (e) {
        logger('[VolcengineSpeechRecognition] 释放录音器失败: $e');
      }
    }

    _isInitialized.value = false;
    logger('[VolcengineSpeechRecognition] 资源已释放');
  }

  /// 便捷方法：切换到普通流式语音识别模式
  void switchToStandardMode() {
    setMode(SpeechRecognitionMode.standard);
  }

  /// 便捷方法：切换到大模型流式语音识别模式
  void switchToBigModelMode() {
    setMode(SpeechRecognitionMode.bigModel);
  }

  /// 便捷方法：检查当前是否为大模型模式
  bool get isBigModelMode => _currentMode == SpeechRecognitionMode.bigModel;

  /// 便捷方法：检查当前是否为普通流式模式
  bool get isStandardMode => _currentMode == SpeechRecognitionMode.standard;

  // ==================== 主动获取最终结果相关方法 ====================

  /// 获取最终结果，如果没有则返回中间结果
  String getResult() {
    if (_finalResult.value.isNotEmpty) {
      return _finalResult.value;
    }
    return _intermediateResult.value;
  }

  /// 检查是否有任何识别结果
  bool get hasResult => _intermediateResult.value.isNotEmpty || _finalResult.value.isNotEmpty;
}
