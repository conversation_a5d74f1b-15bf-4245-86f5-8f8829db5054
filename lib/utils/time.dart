import 'dart:ui';

import 'package:flutter/material.dart';


String formatMilliseconds(int milliseconds) {
  Duration duration = Duration(milliseconds: milliseconds);
  String twoDigits(int n) => n.toString().padLeft(2, '0');
  String minutes = twoDigits(duration.inMinutes.remainder(60));
  String seconds = twoDigits(duration.inSeconds.remainder(60));
  return "$minutes:$seconds";
}

/// 将毫秒转换为分钟（保留两位小数）
double millisecondsToMinutes(int milliseconds) {
  return milliseconds / (1000 * 60);
}

String millisecondsToMinutesString(int milliseconds, {int precision = 2}) {
  return millisecondsToMinutes(milliseconds).toStringAsFixed(precision);
}

/// 将毫秒转换为分钟（整数）
int millisecondsToMinutesInt(int milliseconds) {
  return (milliseconds / (1000 * 60)).round();
}

TextSpan formatMillisecondsSpan(int? milliseconds, {int numFontSize = 30, Color numColor = Colors.black, Color unitColor = const Color(0xff98A2B3)}) {
  if (milliseconds == null || milliseconds == 0) {
    return TextSpan(
      text: '--',
      style: TextStyle(fontSize: 20, color: numColor, fontWeight: FontWeight.bold),
    );
  }
  int hours = milliseconds ~/ 3600000;
  int minutes = (milliseconds % 3600000) ~/ 60000;

  return TextSpan(
    children: [
      if (hours > 0) ...[
        TextSpan(
          text: '$hours',
          style: TextStyle(fontSize: numFontSize.toDouble(), color: numColor, fontWeight: FontWeight.bold),
        ),
        TextSpan(
          text: ' h ',
          style: TextStyle(fontSize: 16, color: unitColor),
        ),
      ],
      TextSpan(
        text: '$minutes',
        style: TextStyle(fontSize: numFontSize.toDouble(), color: numColor, fontWeight: FontWeight.bold),
      ),
      TextSpan(
        text: ' min',
        style: TextStyle(fontSize: 16, color: unitColor),
      ),
    ],
  );
}
