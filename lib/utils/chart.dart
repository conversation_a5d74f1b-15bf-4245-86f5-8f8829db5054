import 'dart:math';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/theme.dart';

import 'util.dart';

class ChartData {
  ChartData({
    this.x,
    this.y,
  });
  num? x;
  num? y;
}

const chartAspectRatio = 1.6;
const chartContainerAspectRatio = 1.2;
const chartContainerParentPadding = EdgeInsets.symmetric(horizontal: 16);
// 获取图表高度
double getChartHeight(EdgeInsets chartContainerPadding) {
  final chartWidth =
      Get.width - chartContainerPadding.left - chartContainerPadding.right - chartContainerParentPadding.left - chartContainerParentPadding.right;
  return chartWidth / chartAspectRatio;
}

// 计算图表中指定索引位置的 Y 坐标（柱状图顶部）
double calculateChartBarTopY(int index, List<ChartData> data, double barBottomY, double chartHeight, double adjustMaxY, double bottomeservedSize) {
  if (data.isEmpty || index >= data.length) return barBottomY;

  final yValue = data[index].y?.toDouble() ?? 0.0;
  final maxY = adjustMaxY;

  // 计算柱状图的可绘制区域高度（减去底部预留空间）
  final drawableHeight = chartHeight - bottomeservedSize;

  // 计算该图表的柱状图高度（使用可绘制区域高度）
  final barHeight = (yValue / maxY) * drawableHeight;

  // 根据底部坐标和柱状图高度计算顶部坐标
  // 柱状图顶部 = 柱状图底部 - 柱状图高度
  final barTopY = barBottomY - barHeight;

  return barTopY;
}

// 构建自定义 tooltip
Widget buildCustomTooltip(CustomTooltipData tooltipData, Color chartColor, EdgeInsets chartContainerPadding) {
  // 动态计算 tooltip 宽度
  final textPainter = TextPainter(
    text: tooltipData.effectiveValueText,
    textDirection: TextDirection.ltr,
    maxLines: 1,
  );
  textPainter.layout(maxWidth: double.infinity);

  final textPainter2 = TextPainter(
    text: tooltipData.effectiveDateText,
    textDirection: TextDirection.ltr,
    maxLines: 1,
  );
  textPainter2.layout(maxWidth: double.infinity);

  // 计算最大文本宽度，并添加内边距
  final maxTextWidth = max(textPainter.width, textPainter2.width);
  final tooltipWidth = maxTextWidth + 20; // 16 是水平内边距 + 4 额外缓冲
  var chartHeight = getChartHeight(chartContainerPadding);

  // 计算图表容器的实际宽度
  final chartContainerWidth =
      Get.width - chartContainerPadding.left - chartContainerPadding.right - chartContainerParentPadding.left - chartContainerParentPadding.right;

  // 计算 tooltip 的理想位置（居中）
  final idealLeft = chartContainerPadding.left + tooltipData.centerX - (tooltipWidth / 2);

  // 计算边界限制
  var minLeft = 10.0; // 左边界
  final maxLeft = chartContainerWidth - tooltipWidth + chartContainerPadding.left + chartContainerPadding.right - 10.0;

  // 调整 tooltip 位置，确保不超出边界
  final adjustedLeft = idealLeft.clamp(minLeft, maxLeft);

  // 计算 tooltip 相对于理想位置的偏移量
  final tooltipOffset = adjustedLeft - idealLeft;

  return Positioned(
    left: adjustedLeft,
    top: 10,
    bottom: chartHeight + chartContainerPadding.bottom - tooltipData.barTopY,
    child: AnimatedOpacity(
      opacity: 1.0,
      duration: const Duration(milliseconds: 200),
      child: Column(
        children: [
          Container(
            width: tooltipWidth,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            decoration: BoxDecoration(
              color: chartColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                RichText(
                  text: tooltipData.effectiveValueText,
                ),
                RichText(
                  text: tooltipData.effectiveDateText,
                ),
              ],
            ),
          ),
          // 连接线：从 tooltip 底部到 chartTop，使用 Transform 来调整虚线位置
          Expanded(
            child: Transform.translate(
              offset: Offset(-tooltipOffset, 0), // 反向偏移，保持虚线居中
              child: CustomPaint(
                painter: DashedLinePainter(
                  color: Colors.black.withOpacity(0.2),
                  strokeWidth: 1,
                  dashWidth: 4,
                  dashSpace: 4,
                ),
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

// 根据数据数量计算柱状图宽度
double calculateBarWidth(int dataCount) {
  if (dataCount <= 7) {
    return 30.0; // 周视图：7个数据，宽度16
  } else if (dataCount <= 12) {
    return 12.0; // 年视图：12个数据，宽度12
  } else if (dataCount <= 31) {
    return 8.0; // 月视图：最多31天，宽度8
  } else {
    return 6.0; // 其他情况：宽度6
  }
}


double calculateYAxisLabels(double maxY) {
  // 根据数据范围智能调整最大值
  double adjustedMaxY;

  if (maxY <= 1.0) {
    // 对于 0.几 的数据，最大值设为 2
    adjustedMaxY = 2.0;
  } else if (maxY <= 3.0) {
    // 对于 1-3 的数据，最大值设为 3
    adjustedMaxY = 3.0;
  } else if (maxY <= 10.0) {
    // 对于 3-10 的数据，向上取整到最近的整数
    adjustedMaxY = maxY.ceil().toDouble();
  } else {
    // 对于大于 10 的数据，向上舍入到最近的 10 的倍数
    adjustedMaxY = ((maxY + 9) ~/ 10) * 10;
    if (maxY % 10 == 0) {
      adjustedMaxY += 10; // 如果 maxY 已经是 10 的倍数，则增加 10
    }
    if (adjustedMaxY < maxY) {
      adjustedMaxY += 10;
    }
  }

  return adjustedMaxY;
}
class CircularIntervalList<T> {
  CircularIntervalList(this._values);
  final List<T> _values;
  int _index = 0;
  T get next {
    if (_index >= _values.length) {
      _index = 0;
    }
    return _values[_index++];
  }
}

class ShortIndicatorDecoration extends Decoration {
  final double indicatorWidth;
  final double indicatorHeight;
  final Color indicatorColor;

  const ShortIndicatorDecoration({
    this.indicatorWidth = 0.5, // 默认宽度为Tab宽度的50%
    this.indicatorHeight = 5.0, // 默认高度为5.0
    this.indicatorColor = Colors.red, // 默认颜色为红色
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _ShortIndicatorPainter(this);
  }
}

class _ShortIndicatorPainter extends BoxPainter {
  final ShortIndicatorDecoration decoration;

  _ShortIndicatorPainter(this.decoration);

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final double indicatorWidth = decoration.indicatorWidth * configuration.size!.width;
    final double horizontalOffset = (configuration.size!.width - indicatorWidth) / 2;
    final Rect rect = Offset(offset.dx + horizontalOffset, offset.dy + configuration.size!.height - decoration.indicatorHeight) &
        Size(indicatorWidth, decoration.indicatorHeight);
    final Paint paint = Paint()
      ..color = decoration.indicatorColor
      ..style = PaintingStyle.fill;

    final RRect rRect = RRect.fromRectAndRadius(rect, Radius.circular(decoration.indicatorHeight / 2));
    canvas.drawRRect(rRect, paint);
  }
}

Pair<int?, int?> getChartTimeRange(int tabType, int index) {
  final now = DateTime.now();
  DateTime startTime;
  DateTime? endTime;

  switch (tabType) {
    case 1:
      startTime = DateTime(now.year, now.month, now.day - index);
      endTime = DateTime(now.year, now.month, now.day - index, 23, 59, 59);
      break;
    case 2:
      int currentWeekday = now.weekday;
      startTime = DateTime(now.year, now.month, now.day - currentWeekday + 1 - index * 7);
      endTime = startTime.add(const Duration(days: 6, hours: 23, minutes: 59, seconds: 59));
      break;
    case 3:
      startTime = DateTime(now.year, now.month - index, 1);
      endTime = DateTime(now.year, now.month - index + 1, 0, 23, 59, 59);
      break;
    case 4:
      startTime = DateTime(now.year - index, 1, 1);
      endTime = DateTime(now.year - index, 12, 31, 23, 59, 59);
      break;
    default:
      throw ArgumentError('Invalid tabType');
  }

  return Pair(
    startTime.millisecondsSinceEpoch,
    endTime.millisecondsSinceEpoch,
  );
}

String getChartTimeRangeString(int tabType, int index) {
  final now = DateTime.now();
  DateTime startTime;
  DateTime? endTime;
  String format(DateTime date) => '${date.year}年${date.month}月${date.day}日';
  String formatWithWeek(DateTime date) => '${date.year}年${date.month}月${date.day}日 周${["一", "二", "三", "四", "五", "六", "日"][date.weekday - 1]}';

  switch (tabType) {
    case 1:
      startTime = now.subtract(Duration(days: index));
      endTime = null;
      if (index == 0) {
        return formatWithWeek(startTime);
      }
      return formatWithWeek(startTime);
    case 2:
      int currentWeekday = now.weekday;
      startTime = now.subtract(Duration(days: currentWeekday - 1 + index * 7));
      endTime = startTime.add(const Duration(days: 6));
      return '${format(startTime)}至${format(endTime)}';
    case 3:
      startTime = DateTime(now.year, now.month - index, 1);
      endTime = DateTime(now.year, now.month - index + 1, 1).subtract(const Duration(days: 1));
      return '${format(startTime)}至${format(endTime)}';
    case 4:
      startTime = DateTime(now.year - index, 1, 1);
      endTime = DateTime(now.year - index, 12, 31);
      return '${startTime.year}年';
    default:
      throw ArgumentError('Invalid tabType');
  }
}

TextSpan formatLs(int? lsTime, {int numSize = 30}) {
  if (lsTime == null) {
    return const TextSpan(
      text: '--',
      style: TextStyle(fontSize: 20),
    );
  }
  return TextSpan(
    children: [
      TextSpan(
        text: '$lsTime',
        style: TextStyle(fontSize: numSize.toDouble(), color: Colors.black, fontWeight: FontWeight.bold),
      ),
      TextSpan(
        text: ' ls',
        style: TextStyle(fontSize: 16, color: gray400),
      ),
    ],
  );
}

// 自定义 tooltip 数据类（精简字段）
class CustomTooltipData {
  final double centerX; // 柱状图中心点在 Stack 中的 X 像素坐标
  final double barTopY; // 柱状图顶部的 Y 像素坐标（如需使用）
  final String valueText; // 展示的数值文本（字符串格式，作为后备）
  final String dateText; // 展示的日期文本（字符串格式，作为后备）
  final TextSpan? valueTextSpan; // 展示的数值文本（TextSpan格式，可选）
  final TextSpan? dateTextSpan; // 展示的日期文本（TextSpan格式，可选）

  CustomTooltipData({
    required this.centerX,
    required this.barTopY,
    required this.valueText,
    required this.dateText,
    this.valueTextSpan,
    this.dateTextSpan,
  });

  // 获取实际使用的 valueText
  TextSpan get effectiveValueText {
    return valueTextSpan ?? TextSpan(
      text: valueText,
      style: const TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
        fontSize: 14,
      ),
    );
  }

  // 获取实际使用的 dateText
  TextSpan get effectiveDateText {
    return dateTextSpan ?? TextSpan(
      text: dateText,
      style: const TextStyle(
        color: Colors.white,
        fontSize: 11,
      ),
    );
  }
}

// 使用示例：
// 
// 1. 使用默认样式（字符串格式）：
// CustomTooltipData(
//   centerX: 100,
//   barTopY: 50,
//   valueText: "15.5分钟 | 85.2分",
//   dateText: "1 LS",
// )
//
// 2. 使用自定义样式（TextSpan格式）：
// CustomTooltipData(
//   centerX: 100,
//   barTopY: 50,
//   valueText: "15.5分钟 | 85.2分", // 作为后备
//   dateText: "1 LS", // 作为后备
//   valueTextSpan: TextSpan(
//     children: [
//       TextSpan(text: "15.5", style: TextStyle(fontWeight: FontWeight.bold)),
//       TextSpan(text: "分钟 | "),
//       TextSpan(text: "85.2", style: TextStyle(fontWeight: FontWeight.bold)),
//       TextSpan(text: "分"),
//     ],
//   ),
// )

// 虚线绘制器
class DashedLinePainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;

  DashedLinePainter({
    required this.color,
    required this.strokeWidth,
    required this.dashWidth,
    required this.dashSpace,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    double y = 0;
    while (y < size.height) {
      final endY = (y + dashWidth).clamp(0.0, size.height);
      canvas.drawLine(
        Offset(size.width / 2, y),
        Offset(size.width / 2, endY),
        paint,
      );
      y += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// 默认柱状图组件，用于loading状态
class DefaultBarChart extends StatelessWidget {
  const DefaultBarChart({
    super.key,
    this.yValues = const [70, 60, 50, 60, 70, 75, 65, 55, 45, 60, 70, 80],
  });

  final List<double> yValues;

  @override
  Widget build(BuildContext context) {
    // 根据传入的Y值列表生成柱状图数据
    final defaultData = yValues.asMap().entries.map((entry) {
      return BarChartGroupData(
        x: entry.key,
        barRods: [
          BarChartRodData(
            toY: entry.value,
            color: const Color(0xffE6E9F1),
            width: 16,
          ),
        ],
      );
    }).toList();

    return BarChart(
      BarChartData(
        backgroundColor: Colors.transparent,
        alignment: BarChartAlignment.spaceBetween,
        minY: 0,
        maxY: 100,
        barTouchData: const BarTouchData(enabled: false), // 禁用触摸
        titlesData: const FlTitlesData(
          show: true,
          rightTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(show: false), // 隐藏边框
        gridData: const FlGridData(show: false), // 隐藏网格
        barGroups: defaultData,
      ),
    );
  }
}
