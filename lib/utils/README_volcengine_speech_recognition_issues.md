# 火山引擎语音识别问题分析和解决方案

## 问题描述

根据日志分析，当前代码存在以下问题：

```
E/flutter (28348): [ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: WebSocketChannelException: WebSocketException: Connection to 'https://open.volcengineapi.com:0/api/v1/audio/asr?model=speech_01&language=zh&format=pcm&sample_rate=16000&channels=1&enable_timestamp=true&enable_punctuation=true&enable_itn=true#' was not upgraded to websocket
```

## 问题分析

### 1. 错误的API端点
- **当前使用**: `https://open.volcengineapi.com/api/v1/audio/asr`
- **正确端点**: `wss://openspeech.bytedance.com/api/v3/sauc/bigmodel`

### 2. 错误的协议
- **当前实现**: 试图将HTTPS转换为WSS，但火山引擎的语音识别API使用完全不同的端点
- **官方要求**: 直接使用WebSocket协议连接到 `openspeech.bytedance.com`

### 3. 错误的鉴权方式
- **当前实现**: 使用URL参数传递配置
- **官方要求**: 在WebSocket握手的HTTP请求头中添加鉴权信息

### 4. 错误的参数格式
- **当前实现**: 使用简单的JSON消息
- **官方要求**: 使用二进制协议，包含4字节header、payload size和payload

## 官方文档要求

根据火山引擎官方文档，正确的实现需要：

### 鉴权头部
```
X-Api-App-Key: 123456789
X-Api-Access-Key: your-access-key
X-Api-Resource-Id: volc.bigasr.sauc.duration
X-Api-Connect-Id: 随机生成的UUID
```

### 协议流程
1. **建立连接**: WebSocket握手时添加鉴权头部
2. **发送full client request**: 包含音频元数据和服务器参数
3. **发送audio only request**: 包含音频数据
4. **接收full server response**: 包含识别结果

### 二进制协议格式
```
Header (4字节) + Payload Size (4字节) + Payload
```

## 当前修复状态

### ✅ 已修复
1. 更新了API端点为 `wss://openspeech.bytedance.com/api/v3/sauc/bigmodel`
2. 更新了配置参数，使其符合官方文档要求
3. 移除了错误的URL参数构建逻辑
4. 添加了警告日志，说明当前实现不符合官方协议

### ❌ 仍需实现
1. **鉴权头部**: Flutter的WebSocketChannel.connect()不支持自定义HTTP头部
   - 需要实现 `X-Api-App-Key`、`X-Api-Access-Key`、`X-Api-Resource-Id` 等鉴权头部
   - 这些参数在代码中已定义但无法通过当前WebSocket实现传递
   - **当前状态**: 使用基本WebSocket连接，鉴权头部无法传递，连接会被服务器拒绝
2. **二进制协议**: 需要实现完整的4字节header + payload size + payload格式
   - **当前状态**: 已实现二进制消息构建，但需要正确的WebSocket连接
3. **协议流程**: 需要实现full client request → audio only request → full server response的完整流程
   - **当前状态**: 已实现消息发送逻辑，但WebSocket连接问题阻止了完整流程

### 🚀 **下一步计划**
1. **实现原生WebSocket**: 创建Flutter插件，支持自定义HTTP头部
2. **测试鉴权流程**: 确保鉴权头部能正确传递到火山引擎服务器
3. **完善错误处理**: 根据服务器响应优化错误处理逻辑

### ✅ **根据官方demo的改进**
1. **修复了头部名称**: 从 `X-Api-Connect-Id` 改为 `X-Api-Request-Id`
2. **添加了sequence字段**: 实现了正确的4字节header + sequence + payload size + payload格式
3. **修复了消息标志位**: 根据官方demo设置了正确的message flags
4. **实现了序列号管理**: 每次发送消息后递增sequence number

## 解决方案

### 方案1: 使用原生WebSocket实现（推荐）
- 创建原生Android/iOS插件
- 实现自定义HTTP头部的WebSocket连接
- 实现完整的二进制协议

### 方案2: 使用HTTP API替代
- 如果火山引擎支持HTTP API，可以考虑使用HTTP POST请求
- 避免WebSocket协议的复杂性

### 方案3: 使用第三方库
- 寻找支持自定义HTTP头部的WebSocket库
- 实现二进制协议的消息构建

## 临时解决方案

当前代码已经更新为使用正确的API端点，但仍然是临时实现：

1. **连接**: 可以建立WebSocket连接，但缺少鉴权
2. **消息**: 使用简单的JSON格式，不符合二进制协议要求
3. **功能**: 基本功能可用，但可能被服务器拒绝

## 下一步行动

1. **获取正确的API凭证**: 从火山引擎控制台获取APP ID和Access Token
2. **实现鉴权**: 解决WebSocket握手时的HTTP头部问题
3. **实现二进制协议**: 按照官方文档实现完整的消息格式
4. **测试验证**: 确保与火山引擎API的兼容性

## 注意事项

- 当前实现仅用于开发和测试
- 生产环境需要完整的鉴权和协议实现
- 建议联系火山引擎技术支持获取更多帮助
