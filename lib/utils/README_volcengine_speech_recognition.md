# 火山引擎大模型流式语音识别工具类

## 概述

这是一个专门用于处理火山引擎大模型流式语音识别的Flutter工具类，基于火山引擎官方文档 [https://www.volcengine.com/docs/6561/1354869](https://www.volcengine.com/docs/6561/1354869) 开发。

## 功能特性

- ✅ 支持实时录音和流式语音识别（集成flutter_sound）
- ✅ 支持流式语音识别（实时返回中间结果）
- ✅ 支持单次语音识别
- ✅ 自动处理API认证和签名
- ✅ 响应式状态管理（基于GetX）
- ✅ 完整的错误处理和日志记录
- ✅ 支持多种音频格式和语言
- ✅ 可配置的识别参数
- ✅ WebSocket连接实现真正的流式通信
- ✅ 自动录音权限检查和请求

## 文件结构

```
lib/utils/
├── volcengine_speech_recognition.dart          # 核心工具类（WebSocket + 语音识别）
├── audio_recorder_utils.dart                   # 录音工具类（flutter_sound集成）
├── volcengine_speech_recognition_example.dart  # 使用示例
└── README_volcengine_speech_recognition.md     # 使用说明
```

## 快速开始

### 1. 配置API密钥

首先需要在火山引擎控制台获取Access Key和Secret Key，然后修改工具类中的固定密钥：

```dart
// 在 lib/utils/volcengine_speech_recognition.dart 文件中修改
class VolcengineSpeechRecognition {
  // 固定的API密钥 - 请根据实际情况修改
  static const String _accessKey = 'your_access_key_here';
  static const String _secretKey = 'your_secret_key_here';
}
```

### 2. 初始化语音识别服务

```dart
import 'package:lsenglish/utils/volcengine_speech_recognition.dart';

final speechRecognition = VolcengineSpeechRecognition();

// 初始化服务
final success = await speechRecognition.initialize();
```

if (success) {
  print('语音识别服务初始化成功');
} else {
  print('语音识别服务初始化失败');
}
```

### 3. 开始实时录音和流式识别

```dart
// 方式1：实时录音识别（推荐）
final success = await speechRecognition.startRealTimeRecording(
  params: {
    'language': 'zh', // 中文识别
    'enable_timestamp': true,
    'enable_punctuation': true,
  },
);

// 方式2：使用预录制音频进行流式识别
Uint8List audioData = getAudioData(); // 从录音或文件获取
final success = await speechRecognition.startStreamingRecognition(
  audioData: audioData,
  params: {
    'language': 'zh', // 中文识别
    'enable_timestamp': true,
    'enable_punctuation': true,
  },
);
```

### 4. 使用独立的录音工具类

```dart
import 'package:lsenglish/utils/audio_recorder_utils.dart';

final audioRecorder = AudioRecorderUtils();

// 初始化录音器
await audioRecorder.init();

// 开始录音并获取实时音频流
await audioRecorder.startRecord(
  (audioData) {
    // 处理实时音频数据
    print('收到音频数据: ${audioData.length} bytes');
  },
  onTimeout: () {
    print('录音超时，自动停止');
  },
  maxDuration: Duration(seconds: 60),
);

// 停止录音
await audioRecorder.stopRecord();

// 释放资源
audioRecorder.dispose();
```

// 监听中间结果
speechRecognition.intermediateResultObs.listen((result) {
  print('中间识别结果: $result');
});

// 监听最终结果
speechRecognition.finalResultObs.listen((result) {
  print('最终识别结果: $result');
});

// 监听错误信息
speechRecognition.errorMessageObs.listen((error) {
  print('错误: $error');
});
```

### 4. 单次语音识别

```dart
// 单次识别（非流式）
final result = await speechRecognition.recognizeAudio(
  audioData: audioData,
  params: {
    'language': 'zh',
    'enable_timestamp': true,
    'enable_punctuation': true,
  },
);

if (result != null) {
  print('识别结果: $result');
}
```

### 5. 停止和清理

```dart
// 停止流式识别
await speechRecognition.stopStreamingRecognition();

// 重置状态
speechRecognition.reset();

// 释放资源
await speechRecognition.dispose();
```

## 配置参数

### 默认参数

```dart
final defaultParams = {
  'model': 'speech_01',           // 模型名称
  'language': 'zh',               // 语言代码
  'format': 'pcm',                // 音频格式
  'sample_rate': 16000,           // 采样率
  'channels': 1,                  // 声道数
  'enable_timestamp': true,       // 是否启用时间戳
  'enable_punctuation': true,     // 是否启用标点符号
  'enable_itn': true,             // 是否启用ITN（数字文本标准化）
};
```

### 支持的语言

- `zh` - 中文
- `en` - 英文
- `ja` - 日文
- `ko` - 韩文
- `fr` - 法文
- `de` - 德文
- `es` - 西班牙文
- `pt` - 葡萄牙文
- `it` - 意大利文
- `ru` - 俄文

### 支持的音频格式

- `pcm` - PCM
- `wav` - WAV
- `mp3` - MP3
- `m4a` - M4A
- `flac` - FLAC

### 支持的采样率

- 8000 Hz
- 16000 Hz
- 22050 Hz
- 44100 Hz
- 48000 Hz

## 状态管理

工具类使用GetX的响应式状态管理，提供以下状态监听器：

```dart
// 初始化状态
speechRecognition.isInitializedObs

// 录音状态
speechRecognition.isRecordingObs

// 流式状态
speechRecognition.isStreamingObs

// 中间结果
speechRecognition.intermediateResultObs

// 最终结果
speechRecognition.finalResultObs

// 错误信息
speechRecognition.errorMessageObs
```

## 错误处理

工具类提供完整的错误处理机制：

1. **初始化错误**：API密钥无效、网络连接失败等
2. **识别错误**：音频格式不支持、识别失败等
3. **流式错误**：连接中断、数据解析失败等

所有错误都会通过 `errorMessageObs` 通知，并在日志中记录详细信息。

## 使用示例

查看 `volcengine_speech_recognition_example.dart` 文件获取完整的使用示例，包括：

- 配置管理
- 状态显示
- 操作控制
- 结果显示
- 错误处理

## 注意事项

1. **API密钥安全**：请妥善保管API密钥，不要在代码中硬编码
2. **音频数据格式**：确保音频数据符合火山引擎的要求
3. **网络连接**：需要稳定的网络连接以支持流式识别
4. **资源管理**：使用完毕后记得调用 `dispose()` 方法释放资源
5. **错误处理**：始终监听错误状态并做相应处理

## 依赖要求

确保项目中包含以下依赖：

```yaml
dependencies:
  dio: ^5.3.3
  get: ^4.6.6
  crypto: ^3.0.6
  get_storage: ^2.1.1
```

## 技术支持

如果遇到问题，请检查：

1. API密钥是否正确配置
2. 网络连接是否正常
3. 音频数据格式是否符合要求
4. 日志输出中的错误信息

## 更新日志

- v1.0.0 - 初始版本，支持基本的流式语音识别功能
- 支持多种语言和音频格式
- 完整的错误处理和状态管理
- 响应式UI示例
