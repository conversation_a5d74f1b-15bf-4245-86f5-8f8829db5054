import 'dart:async';
import 'dart:math';

import 'package:get_storage/get_storage.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/subtitle/subtitle.dart';
import 'package:lsenglish/model/learning_data_models.dart';
import 'package:lsenglish/utils/log.dart' as log;
import '../net/net.dart';
import 'obs.dart';
import 'toast.dart';
import 'dart:convert'; // Added for jsonEncode

/// 现代化的学习数据统计管理器
class DataCenterTimeManager {
  DataCenterTimeManager._internal();
  static final DataCenterTimeManager _instance = DataCenterTimeManager._internal();
  factory DataCenterTimeManager() => _instance;

  // 核心数据
  final Map<String, SentenceLearningRecord> _sentenceRecords = {};
  CurrentActivity? _currentActivity;

  // 学习会话数据
  String _resourceId = '';
  int _resourceType = 2;
  int _sessionStartTime = 0;
  int _sessionEndTime = 0;
  int _lsTimes = 0;
  bool _isPaused = false;
  int _totalPausedTime = 0; // 累计暂停时间
  int _pauseStartTime = 0; // 暂停开始时间

  // 智能存储相关
  Timer? _backupTimer;

  // 存储键
  static const String _sentenceRecordsKey = 'DataCenterTimeManager_sentenceRecords';
  static const String _sessionDataKey = 'DataCenterTimeManager_sessionData';

  // 配置
  static const Duration _backupInterval = Duration(seconds: 10);
  void logger(String msg) {
    if (openDataCenterTimeManagerLog) {
      log.logger("DataCenterTimeManager $msg");
    }
  }

  /// 初始化管理器
  Future<void> initialize() async {
    logger("initialize start");

    try {
      // 处理数据补偿（如果有的话）
      await _handleDataRecovery();

      logger("initialize completed successfully");
    } catch (e, stackTrace) {
      logger("initialize failed: $e");
      logger("StackTrace: $stackTrace");
      // 初始化失败时清理数据，避免脏数据
      _clearInMemoryData();
      await _clearLocalData(); // 同时清理本地存储数据
    }
  }

  /// 处理数据补偿
  Future<void> _handleDataRecovery() async {
    logger("_handleDataRecovery start");

    try {
      // 检查本地存储中是否有待处理的数据
      final sessionDataJson = GetStorage().read(_sessionDataKey) ?? {};
      if (sessionDataJson is! Map || sessionDataJson.isEmpty) {
        logger("没有待处理的数据");
        return;
      }

      // 提取会话数据
      final resourceId = sessionDataJson['resourceId']?.toString() ?? '';
      final sessionStartTime = sessionDataJson['sessionStartTime'] is int ? sessionDataJson['sessionStartTime'] : 0;
      final sessionEndTime = sessionDataJson['sessionEndTime'] is int ? sessionDataJson['sessionEndTime'] : 0;
      final lsTimes = sessionDataJson['lsTimes'] is int ? sessionDataJson['lsTimes'] : 0;
      final totalPausedTime = sessionDataJson['totalPausedTime'] is int ? sessionDataJson['totalPausedTime'] : 0;

      // 检查数据完整性
      if (sessionStartTime == 0 || resourceId.isEmpty) {
        logger("数据不完整，清理无效数据");
        await _clearLocalData();
        return;
      }

      // 检查会话时间跨度是否合理
      final timeSpan = sessionEndTime - sessionStartTime;
      const maxReasonableSpan = 2 * 60 * 60 * 1000; // 2小时

      if (timeSpan > maxReasonableSpan) {
        logger("会话时间跨度异常(${timeSpan}ms，约${(timeSpan / 1000 / 60).round()}分钟)，超过合理范围，清理异常数据");
        await _clearLocalData();
        return;
      }

      // 计算实际学习时长
      final actualDuration = timeSpan - totalPausedTime;

      // 如果实际学习活动时长超过1分钟，认为学习有效
      if (actualDuration >= 60000) {
        logger("发现有效的学习数据，开始数据补偿上传 (实际学习活动时长: ${actualDuration}ms)");

        // 加载句子学习记录
        final sentenceRecordsJson = GetStorage().read(_sentenceRecordsKey) ?? {};
        final sentenceRecords = <String, SentenceLearningRecord>{};

        if (sentenceRecordsJson is Map) {
          sentenceRecordsJson.forEach((key, value) {
            if (key is String && value is Map) {
              try {
                sentenceRecords[key] = SentenceLearningRecord.fromJson(Map<String, dynamic>.from(value));
              } catch (e) {
                logger("解析句子记录失败 key=$key, error=$e");
              }
            }
          });
        }

        // 构建上传数据
        final sessionData = {
          "resourceId": resourceId,
          "resourceType": sessionDataJson['resourceType'] is int ? sessionDataJson['resourceType'] : 2,
          "sessionStartTime": sessionStartTime,
          "sessionEndTime": sessionEndTime,
          "totalSessionDuration": actualDuration,
          "totalPlayDuration": sentenceRecords.values.fold<int>(0, (sum, record) => sum + record.playTotalDuration),
          "totalRecordDuration": sentenceRecords.values.fold<int>(0, (sum, record) => sum + record.recordTotalDuration),
          "lsTimes": lsTimes > 0 ? lsTimes : 1,
        };

        final sentenceRecordsString = jsonEncode(sentenceRecords.values.map((e) => e.toJson()).toList());
        final uploadDataMap = {
          "sessionData": sessionData,
          "sentenceRecords": sentenceRecordsString,
        };

        // 上传数据
        try {
          await Net.getRestClient().addDataEpisode(uploadDataMap);
          final durationMinutes = (actualDuration / 1000 / 60).round();
          logger("数据补偿上传成功，本次学习$durationMinutes分钟");
          toastInfo("本次已学习$durationMinutes分钟");
        } catch (e) {
          logger("数据补偿上传失败: $e");
          // 上传失败时保留数据，下次启动时重试
          return;
        }
      } else {
        logger("实际学习活动时长不足1分钟 (${actualDuration}ms)，清理无效数据");
      }

      // 清理已处理的数据
      await _clearLocalData();
      logger("数据补偿处理完成");
    } catch (e, stackTrace) {
      logger("_handleDataRecovery error: $e");
      logger("StackTrace: $stackTrace");
      // 出错时清理数据
      await _clearLocalData();
    }
  }

  /// 清理内存数据
  void _clearInMemoryData() {
    _sentenceRecords.clear();
    _currentActivity = null;
    _resourceId = '';
    _resourceType = 2;
    _sessionStartTime = 0;
    _sessionEndTime = 0;
    _lsTimes = 0;
    _totalPausedTime = 0; // 重置累计暂停时间
    _pauseStartTime = 0; // 重置暂停开始时间
  }

  /// 启动定期备份定时器
  void _startBackupTimer() {
    logger("_startBackupTimer");
    _backupTimer?.cancel();
    _backupTimer = Timer.periodic(_backupInterval, (timer) {
      if (!_isPaused) {
        _saveToLocal();
      }
    });
  }

  /// 停止定期备份定时器
  void _stopBackupTimer() {
    logger("_stopBackupTimer");
    _backupTimer?.cancel();
    _backupTimer = null;
  }

  /// 停止学习会话（公共方法）
  void stopSession() {
    logger("stopSession");
    _stopBackupTimer();
    _isPaused = false;
  }

  /// 立即保存到本地存储
  Future<void> _saveToLocal() async {
    logger("_saveToLocal");

    try {
      // 计算并打印学习时长 - 使用当前时间
      final duration = getCurrentSessionDuration();
      logger("_saveToLocal: duration=$duration");
      if (duration > 0) {
        final minutes = duration ~/ 1000 ~/ 60;
        final seconds = (duration ~/ 1000) % 60;
        logger("当前学习时长 - $minutes分$seconds秒 ($duration毫秒)");
      }

      // 保存句子学习记录
      final sentenceRecordsJson = _sentenceRecords.map((key, value) => MapEntry(key, value.toJson()));
      GetStorage().write(_sentenceRecordsKey, sentenceRecordsJson);

      // 保存会话数据 - 使用当前时间作为结束时间
      final endTime = DateTime.now().millisecondsSinceEpoch;
      _sessionEndTime = endTime;
      final sessionData = {
        'resourceId': _resourceId,
        'resourceType': _resourceType,
        'sessionStartTime': _sessionStartTime,
        'sessionEndTime': _sessionEndTime,
        'lsTimes': _lsTimes,
        'totalPausedTime': _totalPausedTime, // 保存累计暂停时间
      };
      GetStorage().write(_sessionDataKey, sessionData);

      logger("_saveToLocal completed");
    } catch (e) {
      logger("_saveToLocal error: $e");
    }
  }

  /// 开始新的学习会话
  void beginSession({
    String? resourceId,
    int? resourceType,
    int? lsTimes,
  }) {
    try {
      if (resourceId?.isNotEmpty == true) {
        logger("beginSession: resourceId=$resourceId, resourceType=$resourceType, lsTimes=$lsTimes");

        // 清理之前的数据
        _clearInMemoryData();

        // 设置新会话
        _resourceId = resourceId!;
        _resourceType = resourceType ?? 2;
        _lsTimes = lsTimes ?? 0;
        _sessionStartTime = DateTime.now().millisecondsSinceEpoch; // 改为毫秒
        _isPaused = false;

        // 立即保存
        _saveToLocal();

        // 启动定期备份定时器
        _startBackupTimer();

        logger("学习会话已开始 - $resourceId");
      } else {
        logger("resourceId为空，跳过会话开始");
      }
    } catch (e) {
      logger("开始会话失败 - $e");
    }
  }

  /// 核心学习时长计算方法 - 统一的计算逻辑
  int _calculateSessionDuration(int endTime) {
    if (_sessionStartTime == 0) return 0;

    final totalDuration = endTime - _sessionStartTime;
    final actualDuration = totalDuration - _totalPausedTime;

    logger("学习时长计算 - 总时长: ${totalDuration}ms, 暂停时长: ${_totalPausedTime}ms, 实际学习时长: ${actualDuration}ms");
    return actualDuration > 0 ? actualDuration : 0;
  }

  /// 获取学习时长（毫秒）- 统一的计算逻辑，考虑暂停时间
  int getSessionDuration() {
    return _calculateSessionDuration(DateTime.now().millisecondsSinceEpoch);
  }

  /// 获取基于本地存储的学习时长（毫秒）- 用于应用重启后检查未上传数据
  int getStoredSessionDuration() {
    // 使用最后活动时间作为结束时间，因为这是基于本地存储的数据
    final endTime = _sessionEndTime;
    if (endTime == 0) return 0; // 如果没有有效的结束时间，返回0

    return _calculateSessionDuration(endTime);
  }

  /// 获取当前学习时长（毫秒）- 直接使用当前时间，考虑暂停时间
  int getCurrentSessionDuration() {
    return getSessionDuration();
  }

  /// 暂停学习
  void pauseSession() {
    try {
      if (_isPaused) return;

      logger("pauseSession");
      _isPaused = true;
      _pauseStartTime = DateTime.now().millisecondsSinceEpoch; // 记录暂停开始时间

      _saveToLocal(); // 暂停时立即保存

      logger("会话已暂停");
    } catch (e) {
      logger("暂停会话失败 - $e");
    }
  }

  /// 恢复学习
  void resumeSession() {
    try {
      if (!_isPaused) return;

      logger("resumeSession");
      final resumeTime = DateTime.now().millisecondsSinceEpoch;
      final pauseDuration = resumeTime - _pauseStartTime;
      _totalPausedTime += pauseDuration; // 累计暂停时间

      _isPaused = false;

      logger("会话已恢复，本次暂停时长: ${pauseDuration}ms，累计暂停时长: ${_totalPausedTime}ms");
    } catch (e) {
      logger("恢复会话失败 - $e");
    }
  }

  /// 记录学习活动
  void _recordActivity({
    required LearningEventType eventType,
    required bool isStart,
    required int subtitleStartTime,
    required int subtitleEndTime,
    required int timestamp,
  }) {
    final sentenceId = '${subtitleStartTime}_$subtitleEndTime';
    logger("_recordActivity: sentenceId=$sentenceId, isStart=$isStart, timestamp=$timestamp");
    if (isStart) {
      // 开始活动
      _currentActivity = CurrentActivity(
        eventType: eventType,
        sentenceId: sentenceId,
        startTime: timestamp,
      );
    } else {
      // 结束活动
      if (_currentActivity == null) return;

      final duration = timestamp - _currentActivity!.startTime;

      if (duration > 0) {
        // 获取或创建句子学习记录
        final record = _sentenceRecords.putIfAbsent(
          sentenceId,
          () => SentenceLearningRecord(
            subtitleStartTime: subtitleStartTime,
            subtitleEndTime: subtitleEndTime,
            subtitleDuration: subtitleEndTime - subtitleStartTime,
          ),
        );

        if (eventType == LearningEventType.play) {
          record.playCount += 1;
          record.playTotalDuration += duration;
        } else if (eventType == LearningEventType.record) {
          record.recordCount += 1;
          record.recordTotalDuration += duration;
        }

        record.lastInteractionTime = timestamp; // 毫秒
      }

      _currentActivity = null;
    }
  }

  /// 清理统计数据
  void clearStats() {
    logger("clearStats");
    _clearInMemoryData();
    _saveToLocal();
  }

  /// 清理本地数据
  Future<void> _clearLocalData() async {
    logger("_clearLocalData");
    GetStorage().remove(_sentenceRecordsKey);
    GetStorage().remove(_sessionDataKey);
  }

  /// 清理所有数据（包括内存和本地存储）
  Future<void> clear() async {
    logger("clear");
    _stopBackupTimer();
    _clearInMemoryData();
    await _clearLocalData();
  }

  /// 获取会话开始时间
  int get startTime => _sessionStartTime;

  /// 获取会话结束时间
  int get endTime => _sessionEndTime;

  /// 上传学习数据到后端
  Future<bool> uploadLearningData({bool isDataRecovery = false}) async {
    try {
      if (_resourceId.isEmpty || _sessionStartTime == 0) {
        logger("没有有效的学习会话数据");
        return false;
      }

      // 根据是否是数据补偿来选择不同的时间计算方式
      final duration = isDataRecovery ? getStoredSessionDuration() : getCurrentSessionDuration();
      final endTime = isDataRecovery ? _sessionEndTime : DateTime.now().millisecondsSinceEpoch;

      logger("上传学习数据，原始时长: $duration毫秒, 结束时间: $endTime, 是否数据补偿: $isDataRecovery");

      final totalPlayDuration = _sentenceRecords.values.fold<int>(0, (sum, record) => sum + record.playTotalDuration);
      final totalRecordDuration = _sentenceRecords.values.fold<int>(0, (sum, record) => sum + record.recordTotalDuration);

      logger("上传数据检查 - 会话时长: ${duration}ms, 实际学习活动时长: ${duration}ms");

      // 验证数据合理性 - 新的时间计算逻辑
      // 如果时长小于20秒，跳过上传
      // if (duration < 20000) {
      //   logger("实际学习活动时长不足20秒：${duration}ms，跳过上传");
      //   clear();
      //   return false;
      // }

      // 如果时长大于20秒但小于1分钟，按1分钟计算
      int adjustedDuration = duration;
      if (duration >= 20000 && duration < 60000) {
        adjustedDuration = 60000; // 按1分钟计算
        logger("时长${duration}ms大于20秒但小于1分钟，按1分钟计算");
      }

      logger("调整后时长: ${adjustedDuration}ms");

      // 构建 sessionData - 使用调整后的学习时长
      final sessionData = {
        "resourceId": _resourceId,
        "resourceType": _resourceType,
        "sessionStartTime": _sessionStartTime,
        "sessionEndTime": endTime,
        "totalSessionDuration": adjustedDuration, // 使用调整后的学习时长
        "totalPlayDuration": totalPlayDuration,
        "totalRecordDuration": totalRecordDuration,
        "lsTimes": max(_lsTimes, 1),
      };
      // 构建 sentenceRecords 字符串
      final sentenceRecords = jsonEncode(_sentenceRecords.values.map((e) => e.toJson()).toList());
      // 组装最终上传结构
      final uploadDataMap = {
        "sessionData": sessionData,
        "sentenceRecords": sentenceRecords,
      };
      logger("准备上传 ${_sentenceRecords.length} 条句子记录");
      logger("上传数据: $uploadDataMap");
      await Net.getRestClient().addDataEpisode(uploadDataMap);
      final durationMinutes = (adjustedDuration / 1000 / 60).round();
      logger("上传成功，本次学习$durationMinutes分钟");
      toastInfo("本次已学习$durationMinutes分钟");

      // 上传成功后清理数据
      await clear();
      clearStats();
      ObsUtil().updateDataCenter.value = DateTime.now().millisecond;

      logger("学习数据上传成功");
      return true;
    } catch (e, stackTrace) {
      logger("上传学习数据失败 - $e");
      logger("StackTrace: $stackTrace");
      return false;
    }
  }

  /// 记录统计数据的内部方法
  void _recordStat({
    required bool isPlay,
    required bool isStart,
    required bool isLSMode,
    required int subtitleStartTime,
    required int subtitleEndTime,
    required int now,
    int? subtitleIndex,
  }) {
    final eventType = isPlay ? LearningEventType.play : LearningEventType.record;

    if (isStart) {
      _recordActivity(
        eventType: eventType,
        isStart: isStart,
        subtitleStartTime: subtitleStartTime,
        subtitleEndTime: subtitleEndTime,
        timestamp: now,
      );
    } else {
      _recordActivity(
        eventType: eventType,
        isStart: isStart,
        subtitleStartTime: subtitleStartTime,
        subtitleEndTime: subtitleEndTime,
        timestamp: now,
      );
    }
  }

  /// 记录统计数据（公共接口）
  void recordStatWithSubtitles({
    required bool isPlay,
    required bool isStart,
    required int index,
    required bool isLSMode,
    required int now,
    required List<Subtitle> subtitles,
  }) {
    try {
      if (index >= 0 && index < subtitles.length) {
        int subtitleStartTime = subtitles[index].start.inMilliseconds;
        int subtitleEndTime = subtitles[index].end.inMilliseconds;

        _recordStat(
          isPlay: isPlay,
          isStart: isStart,
          isLSMode: isLSMode,
          subtitleStartTime: subtitleStartTime,
          subtitleEndTime: subtitleEndTime,
          now: now,
          subtitleIndex: index,
        );

        final action = isStart ? "开始" : "结束";
        final mode = isLSMode ? "LS" : "普通";
        final type = isPlay ? "播放" : "录音";
        logger("记录$type统计 - $action$type第$index句 ($mode模式)");
      } else {
        final type = isPlay ? "播放" : "录音";
        logger("$type统计记录失败 - 索引越界: $index/${subtitles.length}");
      }
    } catch (e) {
      final type = isPlay ? "播放" : "录音";
      logger("记录$type统计失败 - $e");
    }
  }

  /// 获取学习统计摘要
  Map<String, dynamic> getLearningStatsSummary() {
    final totalSentences = _sentenceRecords.length;
    final totalPlayCount = _sentenceRecords.values.fold<int>(0, (sum, record) => sum + record.playCount);
    final totalRecordCount = _sentenceRecords.values.fold<int>(0, (sum, record) => sum + record.recordCount);
    return {
      'totalSentences': totalSentences,
      'totalPlayCount': totalPlayCount,
      'totalRecordCount': totalRecordCount,
      'sessionDuration': getStoredSessionDuration(), // 毫秒
    };
  }

  /// 打印学习统计摘要（用于调试）
  void logLearningStatsSummary() {
    try {
      final summary = getLearningStatsSummary();
      logger("学习统计摘要:");
      logger("- 总句子数: ${summary['totalSentences']}");
      logger("- 总播放次数: ${summary['totalPlayCount']}");
      logger("- 总录音次数: ${summary['totalRecordCount']}");
      logger("- 会话时长: ${summary['sessionDuration']}毫秒");
    } catch (e) {
      logger("获取统计摘要失败 - $e");
    }
  }

  /// 销毁管理器，清理资源
  void dispose() {
    logger("dispose");
    _stopBackupTimer();

    // 最后一次保存
    _saveToLocal();
  }
}
