import 'package:flutter/material.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/volcengine_speech_recognition.dart';
import 'package:lsenglish/utils/audio_recorder_utils.dart';

/// 主函数 - 可独立运行的示例应用
void main() {
  runApp(const VolcengineSpeechRecognitionApp());
}

/// 示例应用主Widget
class VolcengineSpeechRecognitionApp extends StatelessWidget {
  const VolcengineSpeechRecognitionApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '火山引擎语音识别示例',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const VolcengineSpeechRecognitionExample(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// 火山引擎语音识别使用示例页面
class VolcengineSpeechRecognitionExample extends StatefulWidget {
  const VolcengineSpeechRecognitionExample({super.key});

  @override
  State<VolcengineSpeechRecognitionExample> createState() => _VolcengineSpeechRecognitionExampleState();
}

class _VolcengineSpeechRecognitionExampleState extends State<VolcengineSpeechRecognitionExample> {
  final VolcengineSpeechRecognition _speechRecognition = VolcengineSpeechRecognition();
  final AudioRecorderUtils _audioRecorder = AudioRecorderUtils();

  @override
  void initState() {
    super.initState();
    _setupListeners();
  }

  @override
  void dispose() {
    _speechRecognition.dispose();
    _audioRecorder.dispose();
    super.dispose();
  }

  /// 设置监听器
  void _setupListeners() {
    // 监听中间结果 - 实时更新UI
    _speechRecognition.intermediateResultObs.listen((result) {
      logger('VolcengineSpeechRecognition intermediateResult: $result');
      setState(() {
        // 触发UI更新，实时显示中间结果
      });
    });

    // 监听最终结果
    _speechRecognition.finalResultObs.listen((result) {
      if (result.isNotEmpty) {
        _showSnackBar('最终识别结果: $result');
        setState(() {
          // 触发UI更新
        });
      }
    });

    // 监听错误信息
    _speechRecognition.errorMessageObs.listen((error) {
      if (error.isNotEmpty) {
        _showSnackBar('错误: $error', isError: true);
        setState(() {
          // 触发UI更新
        });
      }
    });

    // 监听录音状态
    _speechRecognition.isRecordingObs.listen((isRecording) {
      setState(() {});
    });

    // 监听流式状态
    _speechRecognition.isStreamingObs.listen((isStreaming) {
      setState(() {});
    });
  }

  /// 显示提示信息
  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 初始化语音识别服务
  Future<void> _initializeService() async {
    final success = await _speechRecognition.initialize();

    if (success) {
      _showSnackBar('语音识别服务初始化成功');
    } else {
      _showSnackBar('语音识别服务初始化失败', isError: true);
    }
  }

  /// 切换到大模型模式
  void _switchToBigModelMode() {
    _speechRecognition.switchToBigModelMode();
    _showSnackBar('已切换到大模型流式语音识别模式');
    setState(() {});
  }

  /// 切换到普通流式模式
  void _switchToStandardMode() {
    _speechRecognition.switchToStandardMode();
    _showSnackBar('已切换到普通流式语音识别模式');
    setState(() {});
  }

  /// 开始实时录音和流式识别
  Future<void> _startRealTimeRecording() async {
    if (!_speechRecognition.isInitialized) {
      _showSnackBar('请先初始化语音识别服务', isError: true);
      return;
    }

    final success = await _speechRecognition.startRealTimeRecording();

    if (success) {
      _showSnackBar('开始实时录音识别');
    } else {
      _showSnackBar('开始实时录音失败', isError: true);
    }
  }

  /// 停止流式语音识别
  Future<void> _stopStreamingRecognition() async {
    await _speechRecognition.stopStreamingRecognition();
    _showSnackBar('已停止流式识别');
  }

  /// 重置状态
  void _reset() {
    _speechRecognition.reset();
    _showSnackBar('状态已重置');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('火山引擎语音识别示例'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 模式选择区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '识别模式选择',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _switchToBigModelMode,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _speechRecognition.isBigModelMode ? Colors.blue : Colors.grey,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('大模型模式'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _switchToStandardMode,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _speechRecognition.isStandardMode ? Colors.blue : Colors.grey,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('普通流式模式'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '当前模式: ${_speechRecognition.currentMode.name}',
                      style: const TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 配置区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _initializeService,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('初始化服务'),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 状态显示区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '服务状态',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    _buildStatusRow('初始化状态', _speechRecognition.isInitialized),
                    _buildStatusRow('录音状态', _speechRecognition.isRecording),
                    _buildStatusRow('流式状态', _speechRecognition.isStreaming),
                    _buildStatusRow('录音器状态', _audioRecorder.isInitialized),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 操作区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '操作控制',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _reset,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('重置状态'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _speechRecognition.isStreaming ? null : _startRealTimeRecording,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('开始实时录音识别'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _speechRecognition.isStreaming ? _stopStreamingRecognition : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('停止识别'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 实时流式结果显示区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text(
                          '实时识别结果',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(width: 8),
                        if (_speechRecognition.isStreaming)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    _buildRealTimeResultDisplay(),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 历史结果显示区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '历史结果',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    _buildResultRow('最终结果', _speechRecognition.finalResult),
                    _buildResultRow('错误信息', _speechRecognition.errorMessage, isError: true),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建状态行
  Widget _buildStatusRow(String label, bool value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Text('$label: '),
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: value ? Colors.green : Colors.red,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(value ? '是' : '否'),
        ],
      ),
    );
  }

  /// 构建实时结果显示
  Widget _buildRealTimeResultDisplay() {
    final intermediateResult = _speechRecognition.intermediateResult;
    final isStreaming = _speechRecognition.isStreaming;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: isStreaming ? Colors.blue.shade50 : Colors.grey.shade50,
        border: Border.all(
          color: isStreaming ? Colors.blue.shade200 : Colors.grey.shade300,
          width: isStreaming ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isStreaming ? Icons.mic : Icons.mic_off,
                color: isStreaming ? Colors.blue : Colors.grey,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                isStreaming ? '正在录音识别中...' : '等待开始录音',
                style: TextStyle(
                  color: isStreaming ? Colors.blue.shade700 : Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            height: 80,
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(6),
            ),
            child: SingleChildScrollView(
              child: Text(
                intermediateResult.isEmpty ? (isStreaming ? '正在识别中...' : '点击"开始实时录音识别"按钮开始') : intermediateResult,
                style: TextStyle(
                  fontSize: 16,
                  color: intermediateResult.isEmpty ? Colors.grey.shade500 : Colors.black87,
                  height: 1.4,
                ),
              ),
            ),
          ),
          if (isStreaming && intermediateResult.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '实时识别中...',
                    style: TextStyle(
                      color: Colors.green.shade700,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// 构建结果行
  Widget _buildResultRow(String label, String value, {bool isError = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label:',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: isError ? Colors.red.shade50 : Colors.grey.shade50,
              border: Border.all(
                color: isError ? Colors.red.shade200 : Colors.grey.shade300,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              value.isEmpty ? '暂无数据' : value,
              style: TextStyle(
                color: isError ? Colors.red.shade700 : Colors.grey.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
