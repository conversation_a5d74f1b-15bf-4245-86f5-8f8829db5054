import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_plugin_stkouyu/index.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/model/speech_evaluation_result.dart';

import 'package:lsenglish/utils/log.dart';
import 'package:permission_handler/permission_handler.dart';

// 评测类型枚举
enum SpeechEvalCoreType {
  wordEval,
  wordEvalPro,
  sentEval,
  sentEvalPro,
  paraEval,
}

extension SpeechEvalCoreTypeExt on SpeechEvalCoreType {
  String get coreTypeStr {
    switch (this) {
      case SpeechEvalCoreType.wordEval:
        return "word.eval";
      case SpeechEvalCoreType.wordEvalPro:
        return "word.eval.pro";
      case SpeechEvalCoreType.sentEval:
        return "sent.eval";
      case SpeechEvalCoreType.sentEvalPro:
        return "sent.eval.pro";
      case SpeechEvalCoreType.paraEval:
        return "para.eval";
    }
  }

  int get maxDurationMillis {
    switch (this) {
      case SpeechEvalCoreType.wordEval:
      case SpeechEvalCoreType.wordEvalPro:
        return 20000; // 20s
      case SpeechEvalCoreType.sentEval:
      case SpeechEvalCoreType.sentEvalPro:
        return 90000; // 90s
      case SpeechEvalCoreType.paraEval:
        return 300000; // 300s
    }
  }

  String get description {
    switch (this) {
      case SpeechEvalCoreType.wordEval:
        return "英文单词、音标评测";
      case SpeechEvalCoreType.wordEvalPro:
        return "英文音标、单词自适应年龄段评测";
      case SpeechEvalCoreType.sentEval:
        return "英文句子评测";
      case SpeechEvalCoreType.sentEvalPro:
        return "英文句子自适应年龄段评测";
      case SpeechEvalCoreType.paraEval:
        return "英文段落评测";
    }
  }
}

// FlutterPluginSTkouyu的单例管理器
class _STkouyuManager {
  static final _STkouyuManager _instance = _STkouyuManager._internal();
  factory _STkouyuManager() => _instance;
  _STkouyuManager._internal();

  static _STkouyuManager get instance => _instance;

  final FlutterPluginSTkouyu stkouyuPlugin = FlutterPluginSTkouyu();
  bool _isEngineInited = false;
  String? _currentUserId;

  Future<void> initEngine(String appKey, String secretKey, String userId) async {
    if (_isEngineInited && _currentUserId == userId) {
      return;
    }

    KYEngineSetting kyEngineSetting = KYEngineSetting();
    kyEngineSetting.isOutputLog = false;
    OnInitEngineListener onInitEngineListener = OnInitEngineListener(
      onStartInitEngine: () {
        logger("STkouyuManager 初始化引擎===>");
      },
      onInitEngineSuccess: () {
        logger("STkouyuManager 初始化引擎成功===>");
        _isEngineInited = true;
        _currentUserId = userId;
      },
      onInitEngineFailed: () {
        logger("STkouyuManager 初始化引擎失败===>");
        _isEngineInited = false;
      },
    );
    await stkouyuPlugin.initEngine(appKey, secretKey, userId, kyEngineSetting, onInitEngineListener, false);
  }

  bool get isEngineInited => _isEngineInited;
}

class SpeechEvaluation {
  final String _appKey = "1752032229000537";
  final String _secretKey = "010dae2d10b2ad91b73686bbf0630e6c";

  // 状态通知 - 每个实例独立
  final ValueNotifier<bool> _isEngineInited = ValueNotifier(false);
  final ValueNotifier<bool> _isRecording = ValueNotifier(false);
  final ValueNotifier<SpeechEvaluationResult?> _resultNotifier = ValueNotifier(null);
  final ValueNotifier<String?> _errorNotifier = ValueNotifier(null);
  final ValueNotifier<List<EvaluationWarning>?> _warningNotifier = ValueNotifier(null);

  // 流式结果通知 - 每个实例独立
  final ValueNotifier<SpeechEvaluationResult?> _intermediateResultNotifier = ValueNotifier(null);
  final ValueNotifier<SpeechEvaluationResult?> _finalResultNotifier = ValueNotifier(null);

  // 获取STkouyuManager实例
  _STkouyuManager get _stkouyuManager => _STkouyuManager.instance;

  // Getter 方法
  ValueNotifier<bool> get isEngineInited => _isEngineInited;
  ValueNotifier<bool> get isRecording => _isRecording;
  ValueNotifier<SpeechEvaluationResult?> get resultNotifier => _resultNotifier;
  ValueNotifier<String?> get errorNotifier => _errorNotifier;
  ValueNotifier<List<EvaluationWarning>?> get warningNotifier => _warningNotifier;
  ValueNotifier<SpeechEvaluationResult?> get intermediateResultNotifier => _intermediateResultNotifier;
  ValueNotifier<SpeechEvaluationResult?> get finalResultNotifier => _finalResultNotifier;

  // 流式配置
  bool _enableRealtimeFeedback = false;

  Future<void> init() async {
    if (isEngineInited.value) {
      return;
    }

    try {
      await _STkouyuManager.instance.initEngine(_appKey, _secretKey, Config().currentUser.value.id ?? "");
      _isEngineInited.value = true;
    } catch (e) {
      logger("SpeechEvaluation 初始化引擎失败: $e");
      _errorNotifier.value = "初始化引擎失败: $e";
    }
  }

  void startWord(String refText) {
    _start(refText, SpeechEvalCoreType.wordEval);
  }

  void startSentence(String refText, {String? localAudioFilePath, String? localAudioFileName}) {
    logger("SpeechEvaluation startSentence refText: $refText");
    final wordCount = refText.trim().split(RegExp(r'\s+')).length;
    if (wordCount > 200) {
      _start(refText, SpeechEvalCoreType.paraEval, localAudioFilePath: localAudioFilePath, localAudioFileName: localAudioFileName);
    } else {
      _start(refText, SpeechEvalCoreType.sentEval, localAudioFilePath: localAudioFilePath, localAudioFileName: localAudioFileName);
    }
  }

  void startPara(String refText, {String? localAudioFilePath, String? localAudioFileName}) {
    _start(refText, SpeechEvalCoreType.paraEval, localAudioFilePath: localAudioFilePath, localAudioFileName: localAudioFileName);
  }

  // 流式评测方法
  void startWordWithRealtime(String refText) {
    _enableRealtimeFeedback = true;
    _start(refText, SpeechEvalCoreType.wordEval);
  }

  void startSentenceWithRealtime(String refText) {
    _enableRealtimeFeedback = true;
    final wordCount = refText.trim().split(RegExp(r'\s+')).length;
    if (wordCount > 200) {
      _start(refText, SpeechEvalCoreType.paraEval);
    } else {
      _start(refText, SpeechEvalCoreType.sentEval);
    }
  }

  void startParaWithRealtime(String refText) {
    _enableRealtimeFeedback = true;
    _start(refText, SpeechEvalCoreType.paraEval);
  }

  void _start(
    String refText,
    SpeechEvalCoreType coreType, {
    String? localAudioFilePath,
    String? localAudioFileName,
  }) async {
    _resultNotifier.value = null;
    _errorNotifier.value = null;
    _intermediateResultNotifier.value = null;
    _finalResultNotifier.value = null;
    _isRecording.value = true;
    OnRecordListener onRecordListener = OnRecordListener(
        onStart: () {
          logger("SpeechEvaluation 开始录音===>");
          isRecording.value = true;
        },
        onTick: (num var1, num var2) {},
        onStartRecordFail: (String res) {
          logger("SpeechEvaluation 开启录音失败：$res");
          isRecording.value = false;
          errorNotifier.value = "开启录音失败：$res";
        },
        onRecording: (int vad_status, int sound_intensity) {
          logger("SpeechEvaluation vad_status: $vad_status, sound_intensity: $sound_intensity");
        },
        onRecordEnd: () {
          logger('SpeechEvaluation 录音停止===>');
          isRecording.value = false;
        },
        onScore: (String res) async {
          logger('SpeechEvaluation 评测结果回调===>');
          try {
            final Map<String, dynamic> jsonMap = json.decode(res);
            final result = SpeechEvaluationResult.fromJson(jsonMap);
            // 新增：写入本地文件
            // if (res.isNotEmpty) {
            //   try {
            //     final dir = await getApplicationDocumentsDirectory();
            //     final file = File('${dir.path}/speech_eval_res.txt');
            //     await file.writeAsString(res);
            //     logger('[SpeechEvaluation] res已写入: ${file.path}');
            //   } catch (e) {
            //     logger('[SpeechEvaluation] 写文件出错: ${e.toString()}');
            //   }
            // }
            // 处理流式结果
            _handleStreamingResult(result, res);
          } catch (e) {
            errorNotifier.value = "评测结果解析失败：$e";
            isRecording.value = false;
          }
        });

    KYRecordSetting kyRecordSetting = KYRecordSetting();
    // 基础评测参数
    Map<String, dynamic> requestParams = {
      "refText": refText,
      "coreType": coreType.coreTypeStr,
      // 返回结果是否含音频下载地址。可选值：1、0，0为关闭、1为开启。提示：音频保留7天，如需长期保存，建议下载到自己服务器。
      "attachAudioUrl": 1,
    };

    // 针对段落评测，添加单词维度分数参数
    if (coreType == SpeechEvalCoreType.paraEval) {
      // 返回结果是否含单词维度。可选值：1、0，0为关闭、1为开启
      requestParams["paragraph_need_word_score"] = 1;
    }

    // 如果启用流式反馈，添加realtime_feedback参数
    if (_enableRealtimeFeedback) {
      requestParams["realtime_feedback"] = "1";
    }

    kyRecordSetting.request = requestParams;
    kyRecordSetting.audioType = KYAudioType.mp3;
    kyRecordSetting.duration = coreType.maxDurationMillis;
    kyRecordSetting.recordFilePath = localAudioFilePath;
    kyRecordSetting.recordName = localAudioFileName;

    _stkouyuManager.stkouyuPlugin.start(kyRecordSetting, onRecordListener).then((value) => logger("SpeechEvaluation start result: $value"),
        onError: (e) {
      logger("SpeechEvaluation start onError: $e");
      _errorNotifier.value = "start onError: $e";
      _isRecording.value = false;
    }).catchError((e) {
      logger("SpeechEvaluation start catch err: $e");
      _errorNotifier.value = "start catch err: $e";
      _isRecording.value = false;
    });
  }

  int getScore(SpeechEvaluationResult? evalResult) {
    if (evalResult == null) {
      return -1;
    }
    final result = evalResult.result;
    if (result is SentEvalResult) {
      return result.overall;
    } else if (result is ParaEvalResult) {
      return result.overall;
    } else if (result is WordEvalResult) {
      return result.overall;
    }
    return -1;
  }

  Future<void> stop() async {
    await _stkouyuManager.stkouyuPlugin.stop();
    _isRecording.value = false;
    await stopPlay();
  }

  Future<void> playAudio(String audioUrl) async {
    logger("SpeechEvaluation playAudio audioUrl=$audioUrl");
    await _stkouyuManager.stkouyuPlugin.playWithPath(audioUrl);
  }

  Future<void> stopPlay() async {
    await _stkouyuManager.stkouyuPlugin.stopPlay();
  }

  void cancel() async {
    _stkouyuManager.stkouyuPlugin.cancel();
    _isRecording.value = false;
    _enableRealtimeFeedback = false;
  }

  /// 处理流式结果
  void _handleStreamingResult(SpeechEvaluationResult result, String rawResponse) async {
    logger("SpeechEvaluation _handleStreamingResult result: $result");

    // 更新通用结果通知器（保持向后兼容）
    resultNotifier.value = result;

    // 直接赋值警告信息
    List<EvaluationWarning>? warnings;
    final r = result.result;
    if (r is SentEvalResult) {
      warnings = r.warning;
    } else if (r is ParaEvalResult) {
      warnings = r.warning;
    } else if (r is WordEvalResult) {
      warnings = r.warning;
    } else {
      warnings = null;
    }
    logger("SpeechEvaluation _handleStreamingResult warnings: $warnings");
    warningNotifier.value = (warnings != null && warnings.isNotEmpty) ? warnings : null;

    // 根据eof字段判断是中间结果还是最终结果
    if (result.eof == 0) {
      // 中间结果
      logger('SpeechEvaluation 收到中间结果===> eof: ${result.eof}');
      intermediateResultNotifier.value = result;
    } else if (result.eof == 1) {
      // 最终结果
      logger('SpeechEvaluation 收到最终结果===> eof: ${result.eof}');
      finalResultNotifier.value = result;
      isRecording.value = false;
      _enableRealtimeFeedback = false; // 重置流式配置
    } else {
      // 未知状态，按最终结果处理
      logger('SpeechEvaluation 收到未知状态结果===> eof: ${result.eof}');
      finalResultNotifier.value = result;
      isRecording.value = false;
      _enableRealtimeFeedback = false;
    }
  }

  void playback() async {
    OnPlayListener onPlayListener = OnPlayListener(onStart: () {
      logger('SpeechEvaluation 回放开始===>');
    }, onStartFail: (String str) {
      logger('SpeechEvaluation 回放失败===>');
      errorNotifier.value = "回放失败: $str";
    }, onEnd: () {
      logger('SpeechEvaluation 回放结束===>');
    });
    _stkouyuManager.stkouyuPlugin.playback(onPlayListener);
  }

  Future<String?> getLastRecordPath() async {
    String? recordPath = await _stkouyuManager.stkouyuPlugin.getLastRecordPath();
    if (recordPath != null) {
      logger("SpeechEvaluation 录音路径===>$recordPath");
    }
    return recordPath;
  }

  /// 检查录音和存储权限，全部授权返回true，否则false
  Future<bool> checkPermission() async {
    final statuses = await [
      Permission.microphone,
      Permission.storage,
    ].request();
    final micGranted = statuses[Permission.microphone]?.isGranted ?? false;
    final storageGranted = statuses[Permission.storage]?.isGranted ?? false;
    return micGranted && storageGranted;
  }

  /// 监听中间结果变化
  void addIntermediateResultListener(VoidCallback listener) {
    intermediateResultNotifier.addListener(listener);
  }

  /// 移除中间结果监听器
  void removeIntermediateResultListener(VoidCallback listener) {
    _intermediateResultNotifier.removeListener(listener);
  }

  /// 监听最终结果变化
  void addFinalResultListener(VoidCallback listener) {
    finalResultNotifier.addListener(listener);
  }

  /// 移除最终结果监听器
  void removeFinalResultListener(VoidCallback listener) {
    _finalResultNotifier.removeListener(listener);
  }

  /// 检查是否启用了流式反馈
  bool get isRealtimeFeedbackEnabled => _enableRealtimeFeedback;

  /// 手动启用/禁用流式反馈
  void setRealtimeFeedback(bool enabled) {
    _enableRealtimeFeedback = enabled;
  }

  /// 根据警告code列表返回对应的中文提示文本
  static String? getWarningTextForList(List<EvaluationWarning>? warnings) {
    logger("SpeechEvaluation getWarningTextForList warnings: $warnings");
    if (warnings == null || warnings.isEmpty) return null;
    // 优先级顺序：1001、1002、1004、1006、2001/2002
    for (final w in warnings) {
      switch (w.code) {
        case 1001:
          return "没有发现录音内容哦，请重试";
        case 1002:
          return "声音有一点小哦,可以离麦克风近一点哦";
        case 1004:
          return "周围环境有噪音，请找一个安静的地方";
        case 1006:
          return "检测到非英文音频，请确认只说英文";
        case 2001:
        case 2002:
          return "发生未知错误，请重试";
        default:
          continue;
      }
    }
    return null;
  }

  Future<void> dispose() async {
    // 先停止录音和播放
    await stop();
    await stopPlay();

    // 销毁底层插件
    _stkouyuManager.stkouyuPlugin.dispose();

    // 清理所有 ValueNotifier
    _isEngineInited.dispose();
    _isRecording.dispose();
    _resultNotifier.dispose();
    _errorNotifier.dispose();
    _warningNotifier.dispose();
    _intermediateResultNotifier.dispose();
    _finalResultNotifier.dispose();

    // 重置状态
    _enableRealtimeFeedback = false;
  }
}
