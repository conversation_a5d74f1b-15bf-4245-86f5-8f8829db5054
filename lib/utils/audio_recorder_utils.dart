import 'dart:async';
import 'dart:typed_data';

import 'package:audio_session/audio_session.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:logger/logger.dart';
import 'package:lsenglish/utils/log.dart';

/// 录音工具类
/// 提供录音功能，支持实时音频流获取
class AudioRecorderUtils {
  static final AudioRecorderUtils _instance = AudioRecorderUtils._internal();

  factory AudioRecorderUtils() {
    return _instance;
  }

  AudioRecorderUtils._internal();

  FlutterSoundRecorder? _recorder = FlutterSoundRecorder(logLevel: Level.error);
  Timer? _recordTimer;
  StreamController<Uint8List>? _recordingDataController;
  StreamSubscription<Uint8List>? _recordingDataSubscription;

  // 状态管理
  bool _isInitialized = false;
  bool _isRecording = false;

  // Getter方法
  bool get isInitialized => _isInitialized;
  bool get isRecording => _isRecording;

  /// 初始化录音器
  Future<bool> init() async {
    try {
      // 如果 _recorder 被置空了，这里重新 new 一个
      _recorder ??= FlutterSoundRecorder(logLevel: Level.off);
      await _recorder!.openRecorder();
      await _recorder!.setSubscriptionDuration(const Duration(milliseconds: 100));
      
      // 配置音频会话
      final session = await AudioSession.instance;
      await session.configure(const AudioSessionConfiguration(
        avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
        avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.defaultToSpeaker,
        avAudioSessionMode: AVAudioSessionMode.spokenAudio,
        avAudioSessionRouteSharingPolicy: AVAudioSessionRouteSharingPolicy.defaultPolicy,
        avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
      ));
      
      _isInitialized = true;
      logger('[AudioRecorderUtils] 录音器初始化成功');
      return true;
    } catch (e) {
      logger('[AudioRecorderUtils] 录音器初始化失败: $e');
      return false;
    }
  }

  /// 开始录音
  /// [onAudioData] 音频数据回调函数
  /// [onTimeout] 超时回调函数（可选）
  /// [maxDuration] 最大录音时长，默认60秒
  Future<void> startRecord(
    Function(Uint8List) onAudioData, {
    Function? onTimeout,
    Duration maxDuration = const Duration(seconds: 60),
  }) async {
    if (!_isInitialized) {
      throw Exception('录音器未初始化');
    }
    
    if (_isRecording) {
      logger('[AudioRecorderUtils] 录音已在进行中');
      return;
    }

    try {
      // 创建音频数据流控制器
      _recordingDataController = StreamController<Uint8List>();
      
      // 监听音频数据流
      _recordingDataSubscription = _recordingDataController!.stream.listen(
        (data) {
          onAudioData(data);
        },
        onError: (error) {
          logger('[AudioRecorderUtils] 音频数据流错误: $error');
        },
        onDone: () {
          logger('[AudioRecorderUtils] 音频数据流完成');
        },
      );

      // 开始录音
      await _recorder?.startRecorder(
        codec: Codec.pcm16,
        toStream: _recordingDataController!.sink,
        sampleRate: 16000,
        numChannels: 1,
        bitRate: 16000,
      );

      _isRecording = true;

      // 开始超时计时器
      _recordTimer?.cancel();
      _recordTimer = Timer(maxDuration, () {
        logger('[AudioRecorderUtils] 录音已达${maxDuration.inSeconds}秒，自动结束');
        onTimeout?.call();
        stopRecord();
      });

      logger('[AudioRecorderUtils] 开始录音');
    } catch (e) {
      logger('[AudioRecorderUtils] 开始录音失败: $e');
      rethrow;
    }
  }

  /// 停止录音
  Future<void> stopRecord() async {
    try {
      // 取消计时器
      _recordTimer?.cancel();
      _recordTimer = null;

      // 停止录音
      await _recorder?.stopRecorder();

      // 关闭音频数据流
      await _recordingDataSubscription?.cancel();
      _recordingDataSubscription = null;
      
      await _recordingDataController?.close();
      _recordingDataController = null;

      _isRecording = false;
      logger('[AudioRecorderUtils] 录音已停止');
    } catch (e) {
      logger('[AudioRecorderUtils] 停止录音失败: $e');
      rethrow;
    }
  }

  /// 暂停录音
  Future<void> pauseRecord() async {
    if (!_isRecording) return;
    
    try {
      await _recorder?.pauseRecorder();
      logger('[AudioRecorderUtils] 录音已暂停');
    } catch (e) {
      logger('[AudioRecorderUtils] 暂停录音失败: $e');
    }
  }

  /// 恢复录音
  Future<void> resumeRecord() async {
    if (!_isRecording) return;
    
    try {
      await _recorder?.resumeRecorder();
      logger('[AudioRecorderUtils] 录音已恢复');
    } catch (e) {
      logger('[AudioRecorderUtils] 恢复录音失败: $e');
    }
  }

  /// 获取录音状态
  bool get isPaused => _recorder?.isPaused ?? false;

  /// 获取录音时长（需要手动跟踪）
  Duration? get recordingDuration => null; // 需要手动实现时长跟踪

  /// 获取录音音量（需要手动跟踪）
  double? get recordingVolume => null; // 需要手动实现音量跟踪

  /// 释放资源
  void dispose() {
    try {
      stopRecord();
      _recorder?.closeRecorder();
      _recorder = null;
      _isInitialized = false;
      logger('[AudioRecorderUtils] 录音器资源已释放');
    } catch (e) {
      logger('[AudioRecorderUtils] 释放录音器资源失败: $e');
    }
  }

  /// 重置录音器
  Future<void> reset() async {
    try {
      await stopRecord();
      await _recorder?.closeRecorder();
      _recorder = FlutterSoundRecorder(logLevel: Level.off);
      _isInitialized = false;
      logger('[AudioRecorderUtils] 录音器已重置');
    } catch (e) {
      logger('[AudioRecorderUtils] 重置录音器失败: $e');
    }
  }
}
