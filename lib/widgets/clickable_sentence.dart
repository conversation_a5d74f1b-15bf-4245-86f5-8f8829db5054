import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/utils/image.dart';
import 'package:lsenglish/utils/log.dart';

import 'package:lsenglish/utils/toast.dart';

import '../utils/ls_word_util.dart';
import 'dict_widget.dart';
import 'package:lsenglish/model/speech_evaluation_result.dart';

class ClickableSentence extends StatelessWidget {
  final String sentence;
  final TextStyle? style;
  final bool enableTranslate;
  final bool textSelectable;
  final bool isLandscape;
  final SpeechEvaluationResult? evalResult; // 新：完整评测结果
  final bool centerText;

  const ClickableSentence({
    super.key,
    required this.sentence,
    this.style,
    this.textSelectable = true,
    this.enableTranslate = true,
    this.isLandscape = false,
    this.evalResult,
    this.centerText = false,
  });

  @override
  Widget build(BuildContext context) {
    final wordRegex = RegExp(r"\b\w+['']?\w*\b"); // 只匹配单词
    List<TextSpan> spans = [];
    int lastIndex = 0;

    // 优先用评测结果高亮
    List<SentEvalWord>? evalWordsSent;
    List<ParaEvalWordDetail>? evalWordsPara;
    List<WordEvalWord>? evalWordsWord;
    if (evalResult != null && evalResult!.result != null) {
      final result = evalResult!.result;
      logger("[ClickableSentence] 评测类型: ${result.runtimeType}");
      if (result is SentEvalResult && result.words != null) {
        evalWordsSent = result.words;
      } else if (result is ParaEvalResult && result.sentences != null && result.sentences!.isNotEmpty) {
        evalWordsPara = result.sentences!.expand((s) => s.details ?? <ParaEvalWordDetail>[]).toList();
      } else if (result is WordEvalResult && result.words != null) {
        evalWordsWord = result.words;
      }
    }
    if (evalWordsSent != null && evalWordsSent.isNotEmpty) {
      final compareResult = lsSentWordsCompareWithScore(sentence, evalWordsSent, isLandscape: isLandscape);
      spans = _buildSpans(context, compareResult.spans);
    } else if (evalWordsPara != null && evalWordsPara.isNotEmpty) {
      final compareResult = lsParaWordsCompareWithScore(sentence, evalWordsPara, isLandscape: isLandscape);
      spans = _buildSpans(context, compareResult.spans);
    } else if (evalWordsWord != null && evalWordsWord.isNotEmpty) {
      final compareResult = lsWordWordsCompareWithScore(sentence, evalWordsWord, isLandscape: isLandscape);
      spans = _buildSpans(context, compareResult.spans);
    } else {
      // 没有评测结果，普通分词但每个单词都可点击
      for (var match in wordRegex.allMatches(sentence)) {
        // 添加单词前的文本（包括空格和标点）
        if (match.start > lastIndex) {
          spans.add(TextSpan(
            text: sentence.substring(lastIndex, match.start),
            style: getStyle(),
          ));
        }
        // 添加可点击的单词
        final word = match.group(0)!;
        spans.add(TextSpan(
          text: word,
          style: getStyle(),
          recognizer: !enableTranslate
              ? null
              : (TapGestureRecognizer()
                ..onTap = () {
                  var wordTrim = _removePunctuation(word.trim());
                  logger("wordTrim = $wordTrim");
                  if (_isEnglishWord(wordTrim)) {
                    _showBottomSheet(context, wordTrim);
                  }
                }),
        ));
        lastIndex = match.end;
      }
      // 添加最后一个单词后的文本
      if (lastIndex < sentence.length) {
        spans.add(TextSpan(
          text: sentence.substring(lastIndex),
          style: getStyle(),
        ));
      }
    }
    return textSelectable
        ? GestureDetector(
            onTapDown: (details) {
              // 检查点击是否在工具栏区域外
              final RenderBox? toolbarBox = context.findRenderObject() as RenderBox?;
              if (toolbarBox != null) {
                final localPosition = toolbarBox.globalToLocal(details.globalPosition);
                // 如果点击在工具栏区域外，隐藏工具栏
                if (!toolbarBox.paintBounds.contains(localPosition)) {
                  // 需要延迟执行，避免与工具栏的点击事件冲突
                  Future.delayed(const Duration(milliseconds: 100), () {
                    // 这里需要访问 editableTextState，但在这个上下文中无法直接访问
                    // 所以我们需要通过其他方式来处理
                  });
                }
              }
            },
            child: SelectableText.rich(
              TextSpan(children: spans),
              textAlign: centerText
                  ? TextAlign.center
                  : isLandscape
                      ? TextAlign.center
                      : null,
              contextMenuBuilder: (context, editableTextState) {
                return Stack(
                  children: [
                    // 添加一个透明的全屏手势检测器
                    Positioned.fill(
                      child: GestureDetector(
                        onTap: () {
                          // 关闭工具栏并取消文本选择
                          editableTextState.hideToolbar();
                          // 清除文本选择 - 通过设置光标位置到选择开始位置
                          final currentSelection = editableTextState.textEditingValue.selection;
                          if (currentSelection.isValid) {
                            // 将选择范围设置为光标位置（无选择）
                            editableTextState.userUpdateTextEditingValue(
                              editableTextState.textEditingValue.copyWith(
                                selection: TextSelection.collapsed(
                                  offset: currentSelection.start,
                                ),
                              ),
                              SelectionChangedCause.tap,
                            );
                          }
                        },
                        behavior: HitTestBehavior.translucent,
                      ),
                    ),
                    // 工具栏内容
                    AdaptiveTextSelectionToolbar(
                      anchors: editableTextState.contextMenuAnchors,
                      children: [
                        Container(
                          color: Colors.black,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              _buildMainMenuItem(
                                icon: R.edit,
                                label: '记笔记',
                                onTap: () {
                                  final selectedText = editableTextState.textEditingValue.selection.textInside(
                                    editableTextState.textEditingValue.text,
                                  );
                                  if (selectedText.isNotEmpty) {
                                    // _handleNoteTaking(context, selectedText.trim());
                                  }
                                  editableTextState.hideToolbar();
                                },
                                isFirst: true,
                              ),
                              const SizedBox(width: 8),
                              // AI解析
                              _buildMainMenuItem(
                                icon: R.ai,
                                label: 'AI解析',
                                onTap: () {
                                  final selectedText = editableTextState.textEditingValue.selection.textInside(
                                    editableTextState.textEditingValue.text,
                                  );
                                  if (selectedText.isNotEmpty) {
                                    // _handleAIAnalysis(context, selectedText.trim());
                                  }
                                  editableTextState.hideToolbar();
                                },
                              ),
                              const SizedBox(width: 8),
                              // 跳过播放
                              _buildMainMenuItem(
                                icon: R.copy,
                                label: '复制',
                                onTap: () {
                                  final selectedText = editableTextState.textEditingValue.selection.textInside(
                                    editableTextState.textEditingValue.text,
                                  );
                                  if (selectedText.isNotEmpty) {
                                    Clipboard.setData(ClipboardData(text: selectedText.trim()));
                                    '已复制到剪贴板'.snackbar;
                                  }
                                  editableTextState.hideToolbar();
                                },
                                isLast: true,
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ],
                );
              },
            ),
          )
        : Text.rich(
            TextSpan(children: spans),
            textAlign: centerText
                ? TextAlign.center
                : isLandscape
                    ? TextAlign.center
                    : null,
          );
  }

  List<TextSpan> _buildSpans(BuildContext context, List<TextSpan> sourceSpans) {
    return sourceSpans.map((TextSpan span) {
      return TextSpan(
        text: span.text,
        style: span.style,
        recognizer: !enableTranslate
            ? null
            : (TapGestureRecognizer()
              ..onTap = () {
                var wordTrim = _removePunctuation(span.text.toString().trim());
                logger("wordTrim = $wordTrim");
                if (_isEnglishWord(wordTrim)) {
                  _showBottomSheet(context, wordTrim);
                }
              }),
      );
    }).toList();
  }

  bool _isEnglishWord(String word) {
    // 正则表达式用于检查是否为英文单词，允许撇号
    return RegExp(r"^[a-zA-Z']+$").hasMatch(word);
  }

  String _removePunctuation(String word) {
    // 正则表达式用于移除单词中的标点符号，除了撇号
    return word.replaceAll(RegExp(r"[^\w\s']"), '');
  }

  TextStyle? getStyle() {
    return style ?? Get.textTheme.titleLarge?.copyWith(fontSize: 19, height: 1);
  }

  void _showBottomSheet(BuildContext context, String word, {bool autoPlayDict = true}) async {
    // 使用新的静态方法来显示字典模态框
    DictWidget.showDictModal(context, word, autoPlayDict);
  }

  Widget _buildMainMenuItem({
    required String icon,
    required String label,
    required VoidCallback onTap,
    bool isLast = false,
    bool isFirst = false,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(6),
        child: Container(
          padding: EdgeInsets.only(left: isFirst ? 16 : 8, right: isLast ? 16 : 0, top: 4, bottom: 4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ImageLoader(
                icon,
                size: 20,
                color: Colors.white,
              ),
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 9,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
