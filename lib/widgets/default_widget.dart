import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/image.dart';


import '../r.dart';

class NodataWidget extends StatelessWidget {
  final String url;
  final String message;
  final double imageSize;

  const NodataWidget({
    Key? key,
    this.url = R.no_data,
    required this.message,
    this.imageSize = 200,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ImageLoader(
            url,
            width: imageSize,
            height: imageSize,
          ),
          Gap(8),
          Text(
            message,
            style: Get.textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
