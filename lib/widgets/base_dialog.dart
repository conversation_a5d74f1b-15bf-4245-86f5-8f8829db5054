import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';


class CommonDialog extends StatelessWidget {
  final String title;
  final String subtitle;
  final List<String> options;
  final List<VoidCallback> callbacks;
  const CommonDialog({
    super.key,
    required this.title,
    this.subtitle = "",
    required this.options,
    required this.callbacks,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 270,
        decoration: BoxDecoration(
          color: Get.theme.colorScheme.surface,
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Gap(8),
            Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
            Visibility(
              visible: subtitle != "",
              child: Padding(
                padding: EdgeInsets.only(top: 8),
                child: Text(
                  subtitle,
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
            ...List.generate(options.length, (index) {
              return Column(
                children: [
                  const Divider(),
                  Gap(8),
                  GestureDetector(
                    onTap: () {
                      callbacks[index]();
                      Get.back();
                    },
                    child: Text(
                      options[index],
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Get.theme.primaryColor,
                      ),
                    ),
                  ),
                ],
              );
            }),
            Gap(8),
            const Divider(),
            GestureDetector(
              onTap: () => Get.back(),
              child: Text(
                "取消",
                style: TextStyle(fontSize: 18, color: Get.theme.colorScheme.error),
              ),
            ),
            Gap(16),
          ],
        ),
      ),
    );
  }
}
typedef InputCallback = void Function(String content);
class CommonInputDialog extends StatefulWidget {
  final String title;
  final String inputContent;
  final InputCallback sureCallback;
  const CommonInputDialog({
    super.key,
    required this.title,
    this.inputContent = "",
    required this.sureCallback,
  });

  @override
  State<CommonInputDialog> createState() => _CommonInputDialogState();
}

class _CommonInputDialogState extends State<CommonInputDialog> {
  TextEditingController textEditingController = TextEditingController();
  @override
  void initState() {
    super.initState();
    textEditingController.text = widget.inputContent;
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 270,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Gap(8),
            Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                widget.title,
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
            TextField(
              controller: textEditingController,
            ),
            Gap(8),
            const Divider(),
            GestureDetector(
              onTap: () {
                widget.sureCallback(textEditingController.text);
                Get.back();
              },
              child: Text(
                "确定",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Get.theme.primaryColor,
                ),
              ),
            ),
            Gap(8),
            const Divider(),
            GestureDetector(
              onTap: () => Get.back(),
              child: Text(
                "取消",
                style: TextStyle(fontSize: 18, color: Get.theme.colorScheme.error),
              ),
            ),
            Gap(16),
          ],
        ),
      ),
    );
  }
}
