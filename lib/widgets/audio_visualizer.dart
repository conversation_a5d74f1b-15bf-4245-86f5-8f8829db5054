import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

class AudioVisualizer extends StatefulWidget {
  /// 是否正在录音
  final bool isRecording;

  /// 声音数据（可选），如果不提供则使用随机数据
  final List<double>? audioData;

  /// 柱状条的数量
  final int barCount;

  /// 柱状条的最大高度
  final double maxHeight;

  /// 柱状条的宽度
  final double barWidth;

  /// 柱状条之间的间距
  final double spacing;

  /// 柱状条的颜色
  final Color barColor;

  /// 背景颜色
  final Color backgroundColor;

  /// 动画持续时间
  final Duration animationDuration;

  /// 是否显示边框
  final bool showBorder;

  /// 边框颜色
  final Color borderColor;

  /// 边框宽度
  final double borderWidth;

  const AudioVisualizer({
    super.key,
    this.isRecording = false,
    this.audioData,
    this.barCount = 58,
    this.maxHeight = 60.0,
    this.barWidth = 3.0,
    this.spacing = 2.0,
    this.barColor = Colors.white,
    this.backgroundColor = const Color(0xFF2196F3),
    this.animationDuration = const Duration(milliseconds: 250),
    this.showBorder = false,
    this.borderColor = Colors.white,
    this.borderWidth = 1.0,
  });

  @override
  State<AudioVisualizer> createState() => _AudioVisualizerState();
}

class _AudioVisualizerState extends State<AudioVisualizer> {
  final Random _random = Random();
  Timer? _animationTimer;
  List<double> _barHeights = [];

  @override
  void initState() {
    super.initState();
    _initializeBars();
  }

  @override
  void didUpdateWidget(AudioVisualizer oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果录音状态改变，重新设置动画
    if (oldWidget.isRecording != widget.isRecording) {
      if (widget.isRecording) {
        _startAnimation();
      } else {
        _stopAnimation();
      }
    }

    // 如果音频数据改变，更新柱状条高度
    if (widget.audioData != oldWidget.audioData) {
      _updateBarHeights();
    }
  }

  void _initializeBars() {
    _barHeights = List.generate(
      widget.barCount,
      (index) => widget.maxHeight * 0.5, // 初始高度设为最大值的一半
    );
    if (widget.isRecording) {
      _startAnimation();
    } else {
      _stopAnimation();
    }
  }

  void _startAnimation() {
    _animationTimer?.cancel();

    // 立即更新一次高度，然后开始动画
    _updateBarHeights();

    // 使用更快的更新频率，让动画看起来更流畅
    _animationTimer = Timer.periodic(const Duration(milliseconds: 150), (timer) {
      if (mounted && widget.isRecording) {
        _updateBarHeights();
        setState(() {}); // 触发重建以显示动画
      } else {
        timer.cancel();
      }
    });
  }

  void _stopAnimation() {
    _animationTimer?.cancel();
    // 重置所有柱状条到默认高度（最小值是最大值的一半）
    for (int i = 0; i < widget.barCount; i++) {
      _barHeights[i] = widget.maxHeight * 0.5;
    }
    setState(() {});
  }

  void _updateBarHeights() {
    if (widget.audioData != null && widget.audioData!.isNotEmpty) {
      // 使用提供的音频数据
      for (int i = 0; i < widget.barCount; i++) {
        int dataIndex = i % widget.audioData!.length;
        _barHeights[i] = (widget.audioData![dataIndex] * widget.maxHeight).clamp(widget.maxHeight * 0.5, widget.maxHeight);
      }
    } else {
      // 当录音时，每个柱状图都有随机的动画效果
      if (widget.isRecording) {
        for (int i = 0; i < widget.barCount; i++) {
          // 为每个柱状条生成完全随机的高度
          double randomHeight = widget.maxHeight * 0.5 + 
              (_random.nextDouble() * widget.maxHeight * 0.5);
          _barHeights[i] = randomHeight.clamp(widget.maxHeight * 0.5, widget.maxHeight);
        }
      } else {
        // 非录音状态时，使用波浪形效果
        for (int i = 0; i < widget.barCount; i++) {
          // 计算相对于中心的位置（支持负数）
          double relativePosition = (i - widget.barCount / 2).abs();
          double maxDistance = widget.barCount / 2;
          
          // 创建波浪形的音频可视化效果
          // 中心位置声音最大，向两边递减
          double centerFactor = 1.0 - (relativePosition / maxDistance);
          centerFactor = centerFactor.clamp(0.5, 1.0); // 最小值是最大值的一半

          // 添加时间变化，模拟声音的波动
          double timeVariation = 0.5 + 0.5 * _random.nextDouble();

          // 添加位置相关的变化，让相邻的柱状条有连续性
          double positionVariation = 0.7 + 0.3 * _random.nextDouble();

          // 计算最终高度（基于中心线的总高度）
          double heightRatio = centerFactor * timeVariation * positionVariation;
          _barHeights[i] = (heightRatio * widget.maxHeight).clamp(widget.maxHeight * 0.5, widget.maxHeight); // 最小值是最大值的一半
        }
      }
    }
  }

  @override
  void dispose() {
    _animationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.barCount * (widget.barWidth + widget.spacing) - widget.spacing,
      height: widget.maxHeight + (widget.showBorder ? widget.borderWidth * 2 : 0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(8.0),
        border: widget.showBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: BarChart(
          _createBarChartData(),
          duration: widget.animationDuration,
          swapAnimationDuration: widget.animationDuration,
        ),
      ),
    );
  }

  BarChartData _createBarChartData() {
    return BarChartData(
      barTouchData: const BarTouchData(
        enabled: false, // 禁用触摸交互
      ),
      titlesData: const FlTitlesData(
        show: false, // 不显示标题
        rightTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
      ),
      borderData: FlBorderData(
        show: false, // 不显示边框
      ),
      gridData: const FlGridData(show: false), // 不显示网格
      maxY: widget.maxHeight / 2, // 设置最大 Y 值，控制柱状条高度
      minY: -widget.maxHeight / 2, // 设置最小 Y 值，控制柱状条高度
      barGroups: _createBarGroups(),
    );
  }

  List<BarChartGroupData> _createBarGroups() {
    return List.generate(
      widget.barCount,
      (index) => BarChartGroupData(
        x: index - (widget.barCount ~/ 2), // 让 x 轴从负数开始，实现居中分布
        barRods: [
          BarChartRodData(
            fromY: -_barHeights[index] / 2, // 从中心线向下延伸
            toY: _barHeights[index] / 2,    // 从中心线向上延伸
            color: widget.barColor,
            width: widget.barWidth,
            borderRadius: BorderRadius.circular(widget.barWidth / 2),
            backDrawRodData: BackgroundBarChartRodData(
              show: false, // 不显示背景柱状条
            ),
          ),
        ],
      ),
    );
  }
}
