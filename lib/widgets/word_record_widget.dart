// 单词录音内容组件
import 'package:flutter/material.dart';
import 'package:flutter_sound/public/flutter_sound_player.dart';
import 'package:gap/gap.dart';
import 'package:logger/logger.dart';
import 'package:lsenglish/model/speech_evaluation_result.dart';
import 'package:lsenglish/utils/log.dart';

import 'package:lsenglish/utils/speech_evaluation.dart';
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';
import 'package:lsenglish/app/modules/detail/widgets/record_icon.dart';
import 'package:lsenglish/widgets/wave.dart';

class WordRecordWidget extends StatefulWidget {
  final String word;
  final String? remoteAudioUrl; // 远程音源播放地址

  const WordRecordWidget({
    super.key,
    required this.word,
    this.remoteAudioUrl,
  });

  @override
  State<WordRecordWidget> createState() => _WordRecordWidgetState();

  // 添加一个静态方法来显示字典模态框
  static void showWordRecordModal(BuildContext context, String word, {String? remoteAudioUrl}) {
    WoltModalSheet.show<void>(
      context: context,
      pageListBuilder: (modalContext) {
        return [
          WoltModalSheetPage(
            id: 'word_record_page',
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            trailingNavBarWidget: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close),
            ),
            child: WordRecordPage(word: word, remoteAudioUrl: remoteAudioUrl),
          ),
        ];
      },
    );
  }
}

class _WordRecordWidgetState extends State<WordRecordWidget> {
  @override
  Widget build(BuildContext context) {
    return WordRecordPage(
      word: widget.word,
      remoteAudioUrl: widget.remoteAudioUrl,
    );
  }
}

// 合并后的单词录音页面
class WordRecordPage extends StatefulWidget {
  final String word;
  final String? remoteAudioUrl; // 远程音源播放地址

  const WordRecordPage({
    super.key,
    required this.word,
    this.remoteAudioUrl,
  });

  @override
  State<WordRecordPage> createState() => _WordRecordPageState();
}

class _WordRecordPageState extends State<WordRecordPage> {
  final SpeechEvaluation _speechEvaluation = SpeechEvaluation();
  bool _isRecording = false;
  bool _isEvaluating = false;
  bool _hasResult = false;
  String? _errorMessage;
  SpeechEvaluationResult? _evaluationResult;
  FlutterSoundPlayer soundPlayer = FlutterSoundPlayer(logLevel: Level.off);

  @override
  void initState() {
    super.initState();
    _initSpeechEvaluation();
  }

  Future<void> _initSpeechEvaluation() async {
    try {
      await _speechEvaluation.init();
      // 监听评测结果
      _speechEvaluation.resultNotifier.addListener(_onEvaluationResult);
      _speechEvaluation.errorNotifier.addListener(_onEvaluationError);

      // 初始化完成后自动开始录音
      _autoStartRecording();
    } catch (e) {
      logger("初始化语音评测失败: $e");
      setState(() {
        _errorMessage = "初始化语音评测失败: $e";
      });
    }
    await soundPlayer.openPlayer();
  }

  void _autoStartRecording() {
    // 延迟一小段时间确保UI完全加载后再开始录音
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _startRecording();
      }
    });
  }

  void _onEvaluationResult() {
    final result = _speechEvaluation.resultNotifier.value;
    if (result != null) {
      setState(() {
        _isRecording = false;
        _isEvaluating = false;
        _hasResult = true;
        _evaluationResult = result;
      });
      logger("收到评测结果: ${result.eof}");

      // 评测完成后自动播放录音
      _autoPlayRecording();
    }
  }

  // 自动播放录音
  Future<void> _autoPlayRecording() async {
    try {
      final recordPath = await _speechEvaluation.getLastRecordPath();
      if (recordPath != null && recordPath.isNotEmpty) {
        logger("自动播放录音: $recordPath");
        await _speechEvaluation.playAudio(recordPath);
      } else {
        logger("未找到录音文件路径");
      }
    } catch (e) {
      logger("自动播放录音失败: $e");
    }
  }

  void _onEvaluationError() {
    final error = _speechEvaluation.errorNotifier.value;
    if (error != null) {
      setState(() {
        _errorMessage = error;
        _isRecording = false;
      });
      logger("评测错误: $error");
    }
  }

  // 播放远程音源
  void _playRemoteAudio(String audioUrl) {
    try {
      logger("播放远程音源: $audioUrl");
      // 使用 SpeechEvaluation 的播放功能
      soundPlayer.startPlayer(fromURI: audioUrl);
    } catch (e) {
      logger("播放远程音源失败: $e");
      setState(() {
        _errorMessage = "播放远程音源失败: $e";
      });
    }
  }

  Widget _buildResultSection() {
    final result = _evaluationResult!;
    final wordResult = result.result as WordEvalResult;
    final overallScore = wordResult.overall ?? 0;
    final words = wordResult.words ?? [];

    if (words.isEmpty) {
      return Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.orange[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.orange[200]!),
        ),
        child: Text(
          "暂无评测结果",
          style: TextStyle(
            fontSize: 16,
            color: Colors.orange[700],
          ),
        ),
      );
    }

    final wordData = words.first;
    final phonemes = wordData.phonemes ?? [];
    final phonics = wordData.phonics ?? [];

    return Column(
      children: [
        // 总分
        Text(
          "$overallScore分",
          style: TextStyle(
            fontSize: 26,
            fontWeight: FontWeight.bold,
            color: Colors.green[700],
          ),
        ),

        Gap(16),

        // 音素详情表格
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              // 表头
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        "音标",
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        "实际发音",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        "发音判断",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // 音素行
              ...phonemes.map((phoneme) {
                final isCorrect = phoneme.pronunciation != null && phoneme.pronunciation! >= 80;
                final score = phoneme.pronunciation ?? 0;
                final spell = phonics.firstWhere((ph) => ph.phoneme?.contains(phoneme.phoneme) == true).spell;
                final isSpellMismatch = spell != phoneme.phoneme;
                return Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Colors.grey[300]!),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          "/${phoneme.phoneme ?? ""}/",
                          style: TextStyle(fontSize: 14),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          "/$spell/",
                          style: TextStyle(
                            fontSize: 14,
                            color: isSpellMismatch ? Colors.orange : Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: isCorrect ? Colors.green[100] : Colors.red[100],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            "${isCorrect ? "一致" : "不一致"} ($score分)",
                            style: TextStyle(
                              color: isCorrect ? Colors.green[700] : Colors.red[700],
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ],
          ),
        ),
      ],
    );
  }

  // 构建分段变色的单词显示
  Widget _buildColoredWord(String word) {
    // 根据评测结果中的phonics字段来动态分段
    if (_evaluationResult != null && _evaluationResult!.result is WordEvalResult) {
      final wordResult = _evaluationResult!.result as WordEvalResult;
      if (wordResult.words != null && wordResult.words!.isNotEmpty) {
        final wordData = wordResult.words!.first;
        if (wordData.phonics != null && wordData.phonics!.isNotEmpty) {
          // 使用phonics字段来构建分段显示
          return _buildPhonicsBasedWord(wordData.phonics!);
        }
      }
    }

    // 如果没有评测结果，使用默认显示
    return Text(
      word,
      style: TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
    );
  }

  // 构建分段变色的音标显示
  Widget _buildColoredPhoneme(String word) {
    // 根据评测结果中的phonics字段来动态分段
    if (_evaluationResult != null && _evaluationResult!.result is WordEvalResult) {
      final wordResult = _evaluationResult!.result as WordEvalResult;
      if (wordResult.words != null && wordResult.words!.isNotEmpty) {
        final wordData = wordResult.words!.first;
        if (wordData.phonics != null && wordData.phonics!.isNotEmpty) {
          // 使用phonics字段来构建分段显示
          return _buildPhonicsBasedPhoneme(wordData.phonics!);
        }
      }
    }

    // 如果没有评测结果，使用默认显示
    return Text(
      "/$word/",
      style: TextStyle(
        fontSize: 20,
        color: Colors.black,
      ),
    );
  }

  // 基于phonics字段构建单词分段显示
  Widget _buildPhonicsBasedWord(List<dynamic> phonics) {
    List<Widget> wordParts = [];

    for (int i = 0; i < phonics.length; i++) {
      final phonic = phonics[i];
      final spell = phonic.spell ?? '';
      final overall = phonic.overall ?? 0;

      // 根据得分决定颜色：80分以上用绿色，否则用黑色
      final color = overall >= 80 ? Colors.green[700] : Colors.black;

      wordParts.add(
        Text(
          spell,
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: wordParts,
    );
  }

  // 基于phonics字段构建音标分段显示
  Widget _buildPhonicsBasedPhoneme(List<dynamic> phonics) {
    List<Widget> phonemeParts = [];

    for (int i = 0; i < phonics.length; i++) {
      final phonic = phonics[i];
      final phonemes = phonic.phoneme ?? [];
      final overall = phonic.overall ?? 0;

      // 根据得分决定颜色：80分以上用绿色，否则用黑色
      final color = overall >= 80 ? Colors.green[700] : Colors.black;

      // 构建音标字符串
      String phonemeStr = '';
      if (i == 0) phonemeStr += '/'; // 第一个音标前加左斜杠
      phonemeStr += phonemes.join('');
      if (i == phonics.length - 1) phonemeStr += '/'; // 最后一个音标后加右斜杠

      phonemeParts.add(
        Text(
          phonemeStr,
          style: TextStyle(
            fontSize: 20,
            color: color,
          ),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: phonemeParts,
    );
  }

  Future<void> _startRecording() async {
    try {
      setState(() {
        _isRecording = true;
        _isEvaluating = false;
        _errorMessage = null;
      });

      _speechEvaluation.startWord(widget.word);
    } catch (e) {
      logger("开始录音失败: $e");
      setState(() {
        _errorMessage = "开始录音失败: $e";
        _isRecording = false;
      });
    }
  }

  Future<void> _stopRecording() async {
    logger("停止录音");
    try {
      await _speechEvaluation.stop();
      setState(() {
        _isRecording = false;
        _isEvaluating = true;
      });
    } catch (e) {
      logger("停止录音失败: $e");
      setState(() {
        _errorMessage = "停止录音失败: $e";
        _isRecording = false;
      });
    }
  }

  @override
  void dispose() {
    soundPlayer.closePlayer();
    _speechEvaluation.resultNotifier.removeListener(_onEvaluationResult);
    _speechEvaluation.errorNotifier.removeListener(_onEvaluationError);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 单词显示
          GestureDetector(
            onTap: () {
              // 点击单词播放远程音源
              if (widget.remoteAudioUrl != null) {
                _playRemoteAudio(widget.remoteAudioUrl!);
              }
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  // 单词分段显示
                  _buildColoredWord(widget.word),
                  Gap(8),
                  // 音标分段显示
                  _buildColoredPhoneme(widget.word),
                ],
              ),
            ),
          ),

          Gap(32),

          // 录音按钮
          RecordIcon(
            color: _isRecording ? Colors.red : Colors.green,
            size: 80,
            isRecording: _isRecording,
            onTap: () async {
              await Future.delayed(const Duration(milliseconds: 200));
              if (_isRecording) {
                _stopRecording();
              } else {
                _startRecording();
              }
            },
          ),

          Gap(16),

          // 波形动画
          if (_isRecording)
            SizedBox(
              height: 80,
              child: const WaveformWidget(
                volume: 0.8,
                barWidth: 3,
                barSpacing: 6,
                animationDuration: 500,
                delayBetweenBars: 100,
                minHeight: 20,
                maxHeight: 60,
                randomizeDuration: 300,
                random: true,
                barColor: Colors.red,
              ),
            ),

          // 评测状态
          if (_isEvaluating)
            Text(
              "正在评测...",
              style: TextStyle(
                fontSize: 18,
                color: Colors.orange,
                fontWeight: FontWeight.bold,
              ),
            ),

          // 评测结果
          if (_hasResult && _evaluationResult != null) _buildResultSection(),

          // 错误信息
          if (_errorMessage != null)
            Container(
              padding: EdgeInsets.all(16),
              margin: EdgeInsets.only(top: 16),
              decoration: BoxDecoration(
                color: Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red[200]!),
              ),
              child: Text(
                _errorMessage!,
                style: TextStyle(
                  color: Colors.red[700],
                  fontSize: 14,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
