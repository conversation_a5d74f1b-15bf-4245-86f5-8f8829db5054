// ignore_for_file: public_member_api_docs

// This example demonstrates a simple MediaKit integration with audio_service.
//
// To run this example, use:
//
// flutter run -t lib/example_media_kit_player.dart

import 'dart:async';

import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:lsenglish/utils/log.dart';

// You might want to provide this using dependency injection rather than a
// global variable.
late AudioPlayerHandler _audioHandler;

Future<void> main() async {
  // 确保 Flutter 绑定已初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化 MediaKit
  MediaKit.ensureInitialized();

  // 音频会话配置 - audio_service 会自动处理
  logger("跳过音频会话配置，让 audio_service 自动处理");

  _audioHandler = await AudioService.init(
    builder: () => AudioPlayerHandler(),
    config: const AudioServiceConfig(
      androidNotificationChannelId: 'com.mikaelzero.lsenglish.channel.audio',
      androidNotificationChannelName: 'LS English 视频播放',
      androidNotificationOngoing: true,
      // iOS 特定配置
      fastForwardInterval: const Duration(seconds: 10),
      rewindInterval: const Duration(seconds: 10),
    ),
  );
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MediaKit Audio Service Demo',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const MainScreen(),
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({Key? key}) : super(key: key);

  @override
  _MainScreenState createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with WidgetsBindingObserver {
  late Player _controller;
  late VideoController _videoController;
  bool _audioHandlerSetup = false;

  @override
  void initState() {
    super.initState();

    // 添加应用生命周期监听
    WidgetsBinding.instance.addObserver(this);

    _controller = Player();
    _videoController = VideoController(_controller);

    // 等待播放器初始化完成后再设置 AudioHandler
    _controller.stream.playing.listen((playing) {
      if (playing && !_audioHandlerSetup) {}
    });
    _setupAudioHandler();
    _controller.open(Media('https://sf1-cdn-tos.huoshanstatic.com/obj/media-fe/xgplayer_doc_video/mp4/xgplayer-demo-360p.mp4'), play: false);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      logger("应用恢复前台，重新激活音频会话");
      // 应用恢复前台时，重新激活音频会话
      _reactivateAudioSession();
    }
  }

  void _reactivateAudioSession() async {
    logger("应用恢复前台，重新激活音频会话");
    await _audioHandler.reactivateAudioSession();
  }

  void _setupAudioHandler() {
    _audioHandler.setVideoFunctions(_controller.play, _controller.pause, (position) => _controller.seek(position), // 修正seek方法签名
        () {
      _controller.seek(Duration.zero);
      _controller.pause();
    });

    _audioHandler.initializeStreamController(_controller);
    _audioHandlerSetup = true;
  }

  @override
  void dispose() {
    // 移除应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);
    // Close the stream
    _audioHandler.streamController.close();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('MediaKit Audio Service Demo'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Center(
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: Video(
                  controller: _videoController,
                  controls: NoVideoControls,
                ),
              ),
            ),
            const SizedBox(height: 20),
            // Play/pause/stop buttons.
            StreamBuilder<bool>(
              stream: _audioHandler.playbackState.map((state) => state.playing).distinct(),
              builder: (context, snapshot) {
                final playing = snapshot.data ?? false;
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _button(Icons.fast_rewind, _audioHandler.rewind),
                    if (playing) _button(Icons.pause, _audioHandler.pause) else _button(Icons.play_arrow, _audioHandler.play),
                    _button(Icons.stop, _audioHandler.stop),
                    _button(Icons.fast_forward, _audioHandler.fastForward),
                  ],
                );
              },
            ),
            const SizedBox(height: 20),
            // Display the processing state.
            StreamBuilder<AudioProcessingState>(
              stream: _audioHandler.playbackState.map((state) => state.processingState).distinct(),
              builder: (context, snapshot) {
                final processingState = snapshot.data ?? AudioProcessingState.idle;
                return Text("Processing state: ${(processingState)}");
              },
            ),
            const SizedBox(height: 20),
            // Display current position
            StreamBuilder<Duration>(
              stream: _audioHandler.playbackState.map((state) => state.position).distinct(),
              builder: (context, snapshot) {
                final position = snapshot.data ?? Duration.zero;
                return Text("Position: ${position.inMinutes}:${(position.inSeconds % 60).toString().padLeft(2, '0')}");
              },
            ),
          ],
        ),
      ),
    );
  }

  IconButton _button(IconData iconData, VoidCallback onPressed) => IconButton(
        icon: Icon(iconData),
        iconSize: 64.0,
        onPressed: onPressed,
      );
}

class MediaState {
  final MediaItem? mediaItem;
  final Duration position;

  MediaState(this.mediaItem, this.position);
}

/// An [AudioHandler] for playing a single item with MediaKit.
class AudioPlayerHandler extends BaseAudioHandler with SeekHandler {
  late StreamController<PlaybackState> streamController;
  List<StreamSubscription> _subscriptions = [];
  
  // 添加位置跟踪变量，用于节流
  Duration _lastReportedPosition = Duration.zero;
  DateTime _lastPositionUpdate = DateTime.now();

  static final _item = MediaItem(
    id: '1',
    album: "LS English",
    title: "MediaKit 视频播放示例",
    artist: "LS English",
    duration: const Duration(minutes: 5), // 设置一个合理的时长
    artUri: Uri.parse('https://gips3.baidu.com/it/u=3886271102,3123389489&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960'),
  );

  Function? _videoPlay;
  Function? _videoPause;
  Function? _videoSeek;
  Function? _videoStop;

  void setVideoFunctions(Function play, Function pause, Function seek, Function stop) {
    _videoPlay = play;
    _videoPause = pause;
    _videoSeek = seek;
    _videoStop = stop;
    // 确保 MediaItem 被设置
    logger("AudioPlayerHandler: 设置视频函数，同时设置 MediaItem");
    mediaItem.add(_item);
  }

  // 重新激活音频会话的方法
  Future<void> reactivateAudioSession() async {
    try {
      final session = await AudioSession.instance;
      await session.configure(const AudioSessionConfiguration.music());
      logger("AudioPlayerHandler: 音频会话重新激活成功");

      // 重新设置 MediaItem
      mediaItem.add(_item);
    } catch (e) {
      logger("AudioPlayerHandler: 音频会话重新激活失败: $e");
    }
  }

  /// Initialise our audio handler.
  AudioPlayerHandler() {
    logger("AudioPlayerHandler constructor");
    logger("AudioPlayerHandler: 初始化时设置 MediaItem - ${_item.title}");

    // 确保 MediaItem 被正确设置
    mediaItem.add(_item);

    logger("AudioPlayerHandler: 初始化完成，MediaItem 已设置");
  }

  // In this simple example, we handle only 4 actions: play, pause, seek and
  // stop. Any button press from the Flutter UI, notification, lock screen or
  // headset will be routed through to these 4 methods so that you can handle
  // your audio playback logic in one place.

  @override
  Future<void> play() async {
    logger("AudioPlayerHandler: play");

    // 确保 MediaItem 被设置
    mediaItem.add(_item);

    // 尝试重新激活音频会话
    try {
      final session = await AudioSession.instance;
      await session.configure(const AudioSessionConfiguration.music());
      logger("AudioPlayerHandler: 音频会话重新激活成功");
    } catch (e) {
      logger("AudioPlayerHandler: 音频会话重新激活失败: $e");
    }

    _videoPlay?.call();

    logger("AudioPlayerHandler: 播放状态已更新，应该显示在控制中心");
  }

  @override
  Future<void> pause() async {
    logger("AudioPlayerHandler: pause");
    _videoPause?.call();
  }

  @override
  Future<void> seek(Duration position) async {
    logger("AudioPlayerHandler: seek to ${position.inMilliseconds}ms");
    _videoSeek?.call(position);
  }

  @override
  Future<void> stop() async {
    logger("AudioPlayerHandler: stop");
    _videoStop?.call();
  }

  @override
  Future<void> rewind() async {
    logger("AudioPlayerHandler: rewind");
    // 快退 10 秒
    final currentPosition = streamController.stream.first.then((state) => state.position);
    final newPosition = await currentPosition.then((pos) => pos - const Duration(seconds: 10));
    if (newPosition.inMilliseconds > 0) {
      _videoSeek?.call(newPosition);
    }
  }

  @override
  Future<void> fastForward() async {
    logger("AudioPlayerHandler: fastForward");
    // 快进 10 秒
    final currentPosition = streamController.stream.first.then((state) => state.position);
    final newPosition = await currentPosition.then((pos) => pos + const Duration(seconds: 10));
    _videoSeek?.call(newPosition);
  }

  // 修改 initializeStreamController 方法
  void initializeStreamController(Player? videoPlayerController) {
    if (videoPlayerController == null) return;

    bool _isPlaying() => videoPlayerController.state.playing;

    AudioProcessingState _processingState() {
      // 添加详细的处理状态
      switch (videoPlayerController.state.buffering) {
        case true:
          return AudioProcessingState.buffering;
        case false:
          return videoPlayerController.state.completed ? AudioProcessingState.completed : AudioProcessingState.ready;
      }
    }

    Duration _bufferedPosition() {
      // 使用 MediaKit 提供的缓冲位置
      return videoPlayerController.state.buffer;
    }

    void _updatePlaybackState() {
      final state = PlaybackState(
        controls: [
          MediaControl.rewind,
          if (_isPlaying()) MediaControl.pause else MediaControl.play,
          MediaControl.stop,
          MediaControl.fastForward,
        ],
        systemActions: const {
          MediaAction.seek,
          MediaAction.seekForward,
          MediaAction.seekBackward,
        },
        androidCompactActionIndices: const [0, 1, 3],
        processingState: _processingState(),
        playing: _isPlaying(),
        updatePosition: videoPlayerController.state.position,
        bufferedPosition: _bufferedPosition(),
        speed: videoPlayerController.state.rate,
      );

      logger("AudioPlayerHandler: 更新播放状态 - playing: ${_isPlaying()}, position: ${videoPlayerController.state.position}");
      streamController.add(state);
    }

    // 清理之前的订阅
    for (var sub in _subscriptions) {
      sub.cancel();
    }
    _subscriptions.clear();

    // 创建新的流控制器
    streamController = StreamController<PlaybackState>();

    // 监听所有相关状态变化
    _subscriptions.addAll([
      videoPlayerController.stream.playing.listen((_) {
        logger("AudioPlayerHandler: playing 状态变化");
        _updatePlaybackState();
      }),
      videoPlayerController.stream.position.listen((position) {
        if (defaultTargetPlatform == TargetPlatform.android) {
          // Android 平台应用节流逻辑
          final now = DateTime.now();
          final timeSinceLastUpdate = now.difference(_lastPositionUpdate);
          final positionDiff = (position - _lastReportedPosition).abs();
          
          // 只有当位置变化超过2秒，或者距离上次更新超过1秒时才更新
          if (positionDiff.inSeconds >= 2 || timeSinceLastUpdate.inSeconds >= 1) {
            logger("AudioPlayerHandler: position 变化 - ${position.inSeconds}s (Android 节流后)");
            _lastReportedPosition = position;
            _lastPositionUpdate = now;
            _updatePlaybackState();
          }
        } else {
          // iOS 平台直接更新，保持原有体验
          logger("AudioPlayerHandler: position 变化 - ${position.inSeconds}s (iOS 直接更新)");
          _updatePlaybackState();
        }
      }),
      videoPlayerController.stream.buffering.listen((buffering) {
        logger("AudioPlayerHandler: buffering 状态变化 - $buffering");
        _updatePlaybackState();
      }),
      videoPlayerController.stream.completed.listen((_) {
        logger("AudioPlayerHandler: completed 状态变化");
        _updatePlaybackState();
      }),
      // 移除 buffer 流的监听，因为它变化太频繁
      // videoPlayerController.stream.buffer.listen((_) => _updatePlaybackState()),
      // 监听视频时长变化，更新 MediaItem
      videoPlayerController.stream.duration.listen((duration) {
        if (duration.inMilliseconds > 0) {
          logger("AudioPlayerHandler: 视频加载完成，更新 MediaItem 时长 - ${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}");
          final updatedItem = MediaItem(
            id: _item.id,
            album: _item.album,
            title: _item.title,
            artist: _item.artist,
            duration: duration,
            artUri: _item.artUri,
          );
          mediaItem.add(updatedItem);
        }
      }),
    ]);

    // 关键步骤：将 streamController.stream 绑定到 playbackState
    playbackState.addStream(streamController.stream);

    // 立即发送初始状态
    _updatePlaybackState();

    logger("AudioPlayerHandler: 流控制器初始化完成");
  }
}
