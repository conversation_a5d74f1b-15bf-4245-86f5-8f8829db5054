import 'package:json_annotation/json_annotation.dart';

part 'episode_ls_data.g.dart';

@JsonSerializable()
class EpisodeLsData {
  int? totalLearnDuration;
  int? finishTime;
  int? lsTimes;
  int? averageScore;

  EpisodeLsData({this.totalLearnDuration, this.finishTime, this.lsTimes, this.averageScore});

  factory EpisodeLsData.fromJson(Map<String, dynamic> json) {
    return _$EpisodeLsDataFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EpisodeLsDataToJson(this);
}
