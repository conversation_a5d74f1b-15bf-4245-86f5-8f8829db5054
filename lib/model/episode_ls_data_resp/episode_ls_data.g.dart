// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'episode_ls_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EpisodeLsData _$EpisodeLsDataFromJson(Map<String, dynamic> json) =>
    EpisodeLsData(
      totalLearnDuration: (json['totalLearnDuration'] as num?)?.toInt(),
      finishTime: (json['finishTime'] as num?)?.toInt(),
      lsTimes: (json['lsTimes'] as num?)?.toInt(),
      averageScore: (json['averageScore'] as num?)?.toInt(),
    );

Map<String, dynamic> _$EpisodeLsDataToJson(EpisodeLsData instance) =>
    <String, dynamic>{
      'totalLearnDuration': instance.totalLearnDuration,
      'finishTime': instance.finishTime,
      'lsTimes': instance.lsTimes,
      'averageScore': instance.averageScore,
    };
