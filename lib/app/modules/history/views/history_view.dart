import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:path/path.dart';

import '../controllers/history_controller.dart';

class HistoryView extends GetView<HistoryController> {
  const HistoryView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('HistoryView'),
        centerTitle: true,
      ),
      body: ListView.builder(
        itemCount: controller.localHistorys.length,
        itemBuilder: (BuildContext context, int index) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: ShapeDecoration(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  basenameWithoutExtension(
                      controller.localHistorys[index].videoLocalPath ?? ""),
                  style: Get.textTheme.bodyLarge,
                ),
                Text(
                  "data",
                  style: Get.textTheme.bodyLarge,
                )
              ],
            ),
          );
        },
      ),
    );
  }
}
