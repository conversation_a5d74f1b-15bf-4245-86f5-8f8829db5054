import 'package:flutter/material.dart';
import 'package:focus_detector/focus_detector.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/base/loading_getview.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/widgets/default_widget.dart';
import 'package:lsenglish/utils/image.dart';

import '../controllers/home_controller.dart';

class HomeView extends LoadingGetView<HomeController> {
  const HomeView({Key? key}) : super(key: key);
  @override
  Widget buildSuccess(BuildContext context) {
    return FocusDetector(
      onFocusGained: () => controller.onFocusGained(),
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            '学习',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.w600),
          ),
          centerTitle: false,
          actions: [
            GestureDetector(
              onTap: () => controller.importVideo(context: context),
              child: Padding(
                padding: EdgeInsets.only(right: 16),
                child: Icon(
                  Icons.add,
                  size: 30,
                ),
              ),
            ),
            GestureDetector(
              onTap: () => Get.toNamed(Routes.RESOURCELIB),
              child: Padding(
                padding: EdgeInsets.only(right: 16),
                child: ImageLoader(R.book, size: 24),
              ),
            ),
            // Padding(
            //   padding: EdgeInsets.only(right: 16),
            //   child: Row(
            //     children: [
            //       Text(
            //         "目标",
            //         style: Get.textTheme.bodyLarge,
            //       ),
            //       Gap(4),
            //       ImageLoader(R.edit, size: 18, color: Get.theme.colorScheme.onSecondary)
            //     ],
            //   ),
            // ),
            // GestureDetector(
            //   onTap: () => Get.to(() => const FileManager(), preventDuplicates: false),
            //   child: Padding(
            //     padding: EdgeInsets.all(8),
            //     child: Icon(
            //       Icons.file_open_rounded,
            //       size: 30,
            //     ),
            //   ),
            // ),
          ],
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Container(
                  padding: EdgeInsets.all(12),
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Get.theme.primaryColor,
                  ),
                  child: Row(
                    children: [
                      ImageLoader(R.logo, size: 40),
                      Gap(4),
                      Expanded(
                        child: Text(
                          'LS 训练法介绍及使用方法',
                          style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.w600),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Obx(
                () => controller.watchHistorys.isEmpty ? _buildEmptyHistoryWidget(context) : _buildHistoryListWidget(),
              ),
              Gap(32),
              // _buildCollectWidget(),
              // Gap(32),
              // _buildNoteWidget(),
              // Gap(32),
              // _buildDemoWidget(),
              // Gap(32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHistoryListWidget() {
    return Column(
      children: [
        Gap(16),
        SizedBox(
          height: 270,
          child: Obx(() => ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: controller.watchHistorys.length,
                itemBuilder: (BuildContext context, int index) {
                  return GestureDetector(
                    onTap: () {
                      controller.historyClick(index);
                    },
                    onLongPress: () => controller.historyLongPress(index),
                    child: Padding(
                      padding: EdgeInsets.only(left: index == 0 ? 16 : 8, right: 8),
                      child: SizedBox(
                        width: 320,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              clipBehavior: Clip.antiAlias,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                color: const Color(0xffEFEFF0),
                              ),
                              child: AspectRatio(
                                aspectRatio: 393 / 220,
                                child: Stack(
                                  children: [
                                    Positioned.fill(child: ImageLoader(controller.watchHistorys[index].cover, size: 160)),
                                    Center(child: ImageLoader(R.play, color: Colors.white, size: 45))
                                  ],
                                ),
                              ),
                            ),
                            Gap(16),
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(controller.watchHistorys[index].name,
                                          overflow: TextOverflow.ellipsis, style: TextStyle(fontSize: 19, fontWeight: FontWeight.w600)),
                                      Gap(4),
                                      Text(
                                        "1/100-LS 12 min",
                                        style: TextStyle(fontSize: 16, color: const Color(0xFF625B71), fontWeight: FontWeight.w400),
                                      ),
                                    ],
                                  ),
                                ),
                                // CircularProgressIndicator(
                                //   value: 0.5,
                                //   color: Get.theme.primaryColor,
                                //   strokeWidth: 6,
                                // ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              )),
        ),
      ],
    );
  }

  Widget _buildEmptyHistoryWidget(BuildContext context) {
    return Column(
      children: [
        NodataWidget(
          message: "您没有学习记录哦",
          imageSize: 130,
        ),
        Gap(32),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            GestureDetector(
              onTap: () {
                Get.toNamed(Routes.RESOURCELIB);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  color: const Color(0xff101828),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '资源库',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: const Color(0xFFFCFCFD), fontSize: 18, fontWeight: FontWeight.w600),
                ),
              ),
            ),
            Gap(16),
            GestureDetector(
              onTap: () {
                controller.importVideo(context: context);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  color: const Color(0xffF2F4F7),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '去导入',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: const Color(0xFF101828), fontSize: 18, fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ],
        )
      ],
    );
  }
}
