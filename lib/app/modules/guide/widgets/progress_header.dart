import 'package:flutter/material.dart';
import 'package:gap/gap.dart';


class ProgressHeader extends StatelessWidget {
  final int current;
  final int max;
  final VoidCallback onBack;

  const ProgressHeader({
    super.key,
    required this.current,
    required this.max,
    required this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            IconButton(
              icon: Icon(Icons.arrow_back, size: 28),
              onPressed: onBack,
            ),
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return Stack(
                    children: [
                      // 背景线
                      Container(
                        height: 5,
                        width: constraints.maxWidth,
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      // 进度条
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                        height: 5,
                        width: constraints.maxWidth * (current / max),
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
            Gap(16),
          ],
        ),
      ],
    );
  }
}
