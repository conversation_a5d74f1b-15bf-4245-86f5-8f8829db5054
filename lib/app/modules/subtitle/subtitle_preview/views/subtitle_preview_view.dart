import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:lsenglish/widgets/split_english.dart';

import '../controllers/subtitle_preview_controller.dart';

class SubtitlePreviewView extends GetView<SubtitlePreviewController> {
  const SubtitlePreviewView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SubtitlePreviewView'),
        centerTitle: true,
        actions: [
          GestureDetector(
              onTap: () => controller.complete(),
              child: Padding(
                padding: EdgeInsets.only(right: 16),
                child: Icon(
                  Icons.check_rounded,
                  size: 30,
                ),
              )),
        ],
      ),
      body: Obx(() => ListView.builder(
          scrollDirection: Axis.vertical,
          itemCount: controller.subtitles.length,
          itemBuilder: (BuildContext context, int index) {
            return Padding(
              padding: EdgeInsets.all(8),
              child: Container(
                color: Colors.grey[300],
                child: Padding(
                  padding: EdgeInsets.all(8),
                  child: Column(
                    children: [
                      Text(
                          "字幕序号: ${controller.subtitles[index].subtitleIndex}"),
                      Obx(() => Text(
                          "时间: ${controller.subtitles[index].start}  --- ${controller.subtitles[index].end} ")),
                      SplitEnglishWidget(
                        subtitle: controller.subtitles[index],
                      )
                    ],
                  ),
                ),
              ),
            );
          })),
    );
  }
}
