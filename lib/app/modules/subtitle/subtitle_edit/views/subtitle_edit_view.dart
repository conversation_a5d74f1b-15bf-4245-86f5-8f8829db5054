import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish/base/loading_getview.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/utils/image.dart';

import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:super_tooltip/super_tooltip.dart';

import '../controllers/subtitle_edit_controller.dart';

class SubtitleEditView extends LoadingGetView<SubtitleEditController> {
  const SubtitleEditView({Key? key}) : super(key: key);
  @override
  Widget buildSuccess(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('字幕编辑'),
        centerTitle: true,
        actions: [
          GestureDetector(
              onTap: () => controller.complete(),
              child: Padding(
                padding: EdgeInsets.only(right: 16),
                child: const Icon(Icons.check_rounded),
              )),
        ],
      ),
      body: Obx(() => ListView.builder(
          scrollDirection: Axis.vertical,
          itemCount: controller.subtitles.length,
          controller: controller.itemScrollController,
          itemBuilder: (BuildContext context, int index) {
            var titles = ["跳过播放", "合并上句", "合并下句", "编辑", "删除"];
            var icons = [R.only_lines_active, R.subtitle_merge_pre, R.subtitle_merge_next, R.edit, R.delete];
            return SuperTooltip(
              hasShadow: false,
              arrowLength: 8,
              arrowTipDistance: 20,
              controller: controller.getSTC(index),
              content: Container(
                color: Colors.black,
                height: 50,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: List.generate(
                    titles.length,
                    (rowIndex) => GestureDetector(
                      onTap: () {
                        if (rowIndex == 0) {
                          controller.addSkip(index);
                        } else if (rowIndex == 1) {
                          controller.mergePre(index);
                        } else if (rowIndex == 2) {
                          controller.mergeNext(index);
                        } else if (rowIndex == 3) {
                          controller.editSubtitle(index);
                        } else if (rowIndex == 4) {
                          controller.delete(index);
                        }
                      },
                      child: Container(
                        color: Colors.transparent,
                        width: 60,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Obx(() => ImageLoader(
                                  icons[rowIndex],
                                  color: controller.skipIndexs.contains(index) && rowIndex == 0 ? Colors.white.withAlpha(90) : Colors.white,
                                )),
                            Gap(2),
                            Text(
                              titles[rowIndex],
                              style: const TextStyle(color: Colors.white),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              showBarrier: false,
              backgroundColor: Colors.black,
              borderRadius: 16,
              child: GestureDetector(
                onTap: () {
                  controller.subtitleClick(index);
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  child: AutoScrollTag(
                    key: ValueKey(index),
                    controller: controller.itemScrollController,
                    index: index,
                    highlightColor: Get.theme.primaryColor.withOpacity(0.5),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Obx(() => Text(
                        //     "字幕序号: ${controller.subtitles[index].subtitleIndex + 1} ")),
                        // Gap(8),
                        // Obx(() => Text(
                        //     "时间: ${controller.subtitles[index].start}  --- ${controller.subtitles[index].end} ")),
                        Gap(16),
                        Obx(() => Text(
                              controller.subtitles[index].targetData.isEmpty
                                  ? "本句无英文字幕${controller.skipIndexs.contains(index) ? "(skip)" : ""}"
                                  : "${controller.subtitles[index].targetData}${controller.skipIndexs.contains(index) ? "(skip)" : ""}",
                              style: TextStyle(
                                  color: controller.skipIndexs.contains(index)
                                      ? const Color(0xFF1D192B).withAlpha(70)
                                      : index == controller.currentIndex.value
                                          ? Get.theme.primaryColor
                                          : (Get.isDarkMode ? Colors.white : Colors.black),
                                  fontSize: 19),
                            )),
                        Gap(16),
                      ],
                    ),
                  ),
                ),
              ),
            );
          })),
    );
  }
}
