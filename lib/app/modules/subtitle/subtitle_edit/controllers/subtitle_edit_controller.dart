import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/base/loading_getxcontroller.dart';
import 'package:lsenglish/model/video_time_interval.dart';

import 'package:lsenglish/utils/subtitle.dart';
import 'package:lsenglish/utils/subtitle/src/core/models.dart';
import 'package:lsenglish/utils/subtitle/src/utils/subtitle_controller.dart';
import 'package:lsenglish/utils/util.dart';
import 'package:lsenglish/utils/video.dart';
import 'package:path_provider/path_provider.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:super_tooltip/super_tooltip.dart';

import '../../../../../utils/toast.dart';
import '../views/subtitle_mod_widget.dart';

class SubtitleEditController extends LoadingGetxcontroller {
  final AutoScrollController itemScrollController = AutoScrollController(axis: Axis.vertical);
  var subtitleUrl = "";
  var nativeLangSubtitleUrl = "";
  var originSubtitleUrl = "";
  var resourceId = "";
  var resourceType = 1;
  var subtitles = <Subtitle>[].obs;
  SubtitleController? _subtitleController;
  var index = 0;
  var currentIndex = 0.obs;
  var tooltipsControllerMap = <int, SuperTooltipController?>{};
  var skipIndexs = <int>[].obs;
  List<VideoTimeInterval> skipList = [];
  FToast? fToast;
  var previousStates = <List<int>>[];
  bool _needReupload = false; // 是否需要重新上传字幕文件

  @override
  void onInit() async {
    super.onInit();
    subtitleUrl = Get.arguments?['subtitleUrl'] ?? "";
    nativeLangSubtitleUrl = Get.arguments?['nativeLangSubtitleUrl'] ?? "";
    originSubtitleUrl = Get.arguments?['originSubtitleUrl'] ?? "";
    index = Get.arguments?['index'] ?? 0;
    resourceId = Get.arguments?['resourceId'] ?? "";
    skipList = Get.arguments?['skipList'] ?? [];
    resourceType = Get.arguments?['resourceType'] ?? 2;

    currentIndex.value = index;
    setLoading(message: "加载字幕中...");
    var data = subtitleUrl.isEmpty
        ? await loadMultiSubtitlesInternal(Pair(originSubtitleUrl, nativeLangSubtitleUrl))
        : await loadSubtitlesInternal(subtitleUrl);
    _subtitleController = data.item1;
    subtitles.value = data.item2;
    skipIndexs.value = getIndexsFromIntervalList(skipList, subtitles);
    setSuccess();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToIndex();
    });
    itemScrollController.addListener(_listScrollListen);
  }

  @override
  void onClose() {
    fToast?.removeQueuedCustomToasts();
    fToast?.removeCustomToast();
    itemScrollController.removeListener(_listScrollListen);
    super.onClose();
  }

  void _listScrollListen() {
    tooltipsControllerMap.forEach((index, controller) {
      if (controller != null) {
        controller.hideTooltip();
      }
    });
  }

  void _scrollToIndex() async {
    itemScrollController.jumpTo(70.0 * index); //这里的70需要优化 有些文本长 有些短
    await Future.delayed(const Duration(milliseconds: 100));
    await itemScrollController.scrollToIndex(index, preferPosition: AutoScrollPosition.middle);
    getSTC(index).showTooltip();
  }

  SuperTooltipController getSTC(int index) {
    var stc = tooltipsControllerMap[index];
    if (stc == null) {
      tooltipsControllerMap[index] = SuperTooltipController();
      return tooltipsControllerMap[index]!;
    }
    return stc;
  }

  void subtitleClick(int index) async {
    tooltipsControllerMap.forEach((i, controller) {
      if (i != index && controller != null) {
        controller.hideTooltip();
      }
    });
    currentIndex.value = index;
    getSTC(index).showTooltip();
  }

  Future<void> hideTooltip() async {
    tooltipsControllerMap.forEach((i, controller) async {
      if (controller != null) {
        await controller.hideTooltip();
      }
    });
  }

  void mergePre(int index) {
    if (index == 0) {
      return;
    }
    hideTooltip();
    _subtitleController?.mergeSubtitles(index - 1, index);
    subtitles.value = _subtitleController?.subtitles ?? [];
    subtitles.refresh();
    _needReupload = true; // 标记需要重新上传
    fToast?.removeCustomToast();
    fToast = toastInfoSuccess("已合并上一句");
    updateSkipIndexes(index - 1);
  }

  void mergeNext(int index) {
    if (index >= subtitles.length - 1) {
      return;
    }
    hideTooltip();
    _subtitleController?.mergeSubtitles(index, index + 1);
    subtitles.value = _subtitleController?.subtitles ?? [];
    subtitles.refresh();
    _needReupload = true; // 标记需要重新上传
    fToast?.removeCustomToast();
    fToast = toastInfoSuccess("已合并下一句");
    updateSkipIndexes(index);
  }

  void editSubtitle(int index) async {
    if (_subtitleController?.subtitles[index] == null) {
      return;
    }
    hideTooltip();
    await Get.bottomSheet(
      SubtitleModWidget(
        subtitle: _subtitleController!.subtitles[index],
        saveCallback: (content) {
          _subtitleController!.subtitles[index].targetData = content;
          subtitles.value = _subtitleController?.subtitles ?? [];
          subtitles.refresh();
          _needReupload = true; // 标记需要重新上传
        },
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      isScrollControlled: true,
      enableDrag: false,
    );
    getSTC(index).showTooltip();
  }

  void delete(int index) {
    hideTooltip();
    var deletedSubtitle = _subtitleController?.deleteSubtitle(index);
    subtitles.value = _subtitleController?.subtitles ?? [];
    subtitles.refresh();
    _needReupload = true; // 标记需要重新上传
    fToast?.removeCustomToast();
    fToast = FToast();
    fToast?.init(Get.context!);
    fToast?.showToast(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.all(15),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: const Color(0xFF1D192B).withOpacity(0.8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.check, size: 24, color: Colors.white),
                Gap(10),
                Text("已删除 “", style: Get.textTheme.titleSmall?.copyWith(color: Colors.white)),
                Expanded(
                    child: Text("${deletedSubtitle?.targetData}",
                        overflow: TextOverflow.ellipsis, style: Get.textTheme.titleSmall?.copyWith(color: Colors.white))),
                Text("”", style: Get.textTheme.titleSmall?.copyWith(color: Colors.white)),
                Gap(16),
                GestureDetector(
                  onTap: () {
                    fToast?.removeCustomToast();
                    undoDelete();
                    getSTC(index).showTooltip();
                  },
                  child: Text(
                    "撤销",
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                      decoration: TextDecoration.underline,
                      decorationColor: Colors.white,
                      decorationThickness: 2,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Gap(8),
                Icon(Icons.undo_rounded, size: 24, color: Colors.white),
              ],
            ),
          ),
        ),
        gravity: ToastGravity.BOTTOM,
        toastDuration: const Duration(seconds: 2),
        positionedToastBuilder: (context, child, gravity) {
          return Positioned(
            left: 0,
            right: 0,
            bottom: 20,
            child: child,
          );
        });
    updateSkipIndexes(index);
  }

  void jumpNextTooltips() async {
    await hideTooltip();
    await itemScrollController.scrollToIndex(currentIndex.value, preferPosition: AutoScrollPosition.middle);
    getSTC(currentIndex.value).showTooltip();
  }

  //删除后撤销
  void undoDelete() {
    _subtitleController?.undoDelete();
    subtitles.value = _subtitleController?.subtitles ?? [];
    subtitles.refresh();
    if (previousStates.isNotEmpty) {
      skipIndexs.assignAll(previousStates.removeLast());
    }
  }

  void addSkip(int index) {
    if (skipIndexs.contains(index)) {
      skipIndexs.remove(index);
    } else {
      skipIndexs.add(index);
    }
    currentIndex.value = index + 1;
    jumpNextTooltips();
  }

  void updateSkipIndexes(int index) {
    // 保存当前状态以便撤销
    previousStates.add(List.from(skipIndexs));
    // 如果 skipIndexs 包含 index，先删除它
    skipIndexs.remove(index);

    // 更新比 index 大的元素
    for (int i = 0; i < skipIndexs.length; i++) {
      if (skipIndexs[i] > index) {
        skipIndexs[i]--;
      }
    }
  }

  void complete() async {
    SmartDialog.showLoading(msg: "正在合并中...");
    var filePath = "";
    if (_needReupload) {
      var saveDir = await getApplicationCacheDirectory();
      filePath = "${saveDir.path}/tempSubtitle.srt";
      await _subtitleController?.writeToSrtFile(filePath);
    } else {
      filePath = "";
    }
    await SubtitleUtil().uploadSubtitle(
      resourceId,
      resourceType,
      filePath: filePath,
      // ignore: invalid_use_of_protected_member
      skipList: generateSkipVideoTimeInterval(skipIndexs.value, subtitles.value),
    );
    SmartDialog.dismiss();
    Get.back(result: true);
  }
}
