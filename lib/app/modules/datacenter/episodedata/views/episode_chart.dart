import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/chart.dart';
import 'package:lsenglish/utils/time.dart';

import '../../../../../model/episode_ls_data_resp/episode_ls_data.dart';

typedef SelectIndexCallback = void Function(int?);

class EpisodeChartWidget extends StatefulWidget {
  final List<EpisodeLsData> episodeLsDataList;
  final SelectIndexCallback selectIndexCallback;
  final Function(int index, double value)? onTooltipShow; // tooltip 显示回调
  final VoidCallback? onTooltipHide; // tooltip 隐藏回调
  final int currentPage; // 当前页码
  final int pageSize; // 每页数据量，默认7
  final double adjustMinY;
  final double adjustMaxY;

  const EpisodeChartWidget({
    super.key,
    required this.episodeLsDataList,
    required this.selectIndexCallback,
    this.onTooltipShow, // 添加 tooltip 显示回调
    this.onTooltipHide, // 添加 tooltip 隐藏回调
    this.currentPage = 0,
    this.pageSize = 7,
    this.adjustMinY = 0.0,
    this.adjustMaxY = 100.0,
  });

  @override
  State<EpisodeChartWidget> createState() => EpisodeChartWidgetState();
}

class EpisodeChartWidgetState extends State<EpisodeChartWidget> {
  int? selectedIndex;
  final chartContainerPadding = const EdgeInsets.only(top: 16, bottom: 16, left: 18, right: 14);
  final bottomeservedSize = 20.0;
  bool isLoading = false;
  CustomTooltipData? tooltipData;
  final barWidth = 10.0;

  // 当前页面的数据
  List<ChartData> pageDurationData = [];
  List<ChartData> pageScoreData = [];

  // 时长柱状图颜色
  static const durationColor = Color(0xFF0D99FF);

  @override
  void initState() {
    super.initState();
    _processCurrentPageData();
  }

  @override
  void didUpdateWidget(covariant EpisodeChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.episodeLsDataList != widget.episodeLsDataList || oldWidget.currentPage != widget.currentPage) {
      _processCurrentPageData();
      selectedIndex = null;
      widget.selectIndexCallback(selectedIndex);
    }
  }

  void hideTooltips() {
    setState(() {
      tooltipData = null;
      selectedIndex = null;
    });
  }

  void _processCurrentPageData() {
    // 只处理当前页面的数据
    final startIndex = widget.currentPage * widget.pageSize;
    pageDurationData.clear();
    pageScoreData.clear();

    for (var i = 0; i < widget.pageSize; i++) {
      final dataIndex = startIndex + i;

      if (dataIndex < widget.episodeLsDataList.length) {
        // 有实际数据
        final episodeData = widget.episodeLsDataList[dataIndex];
        final duration = episodeData.totalLearnDuration != null ? millisecondsToMinutes(episodeData.totalLearnDuration!) : 0.0;
        final score = episodeData.averageScore?.toDouble() ?? 0.0;

        pageDurationData.add(ChartData(x: i, y: duration));
        pageScoreData.add(ChartData(x: i, y: score));
      } else {
        // 没有数据，填充0
        pageDurationData.add(ChartData(x: i, y: 0.0));
        pageScoreData.add(ChartData(x: i, y: 0.0));
      }
    }
  }

  @override
  void dispose() {
    selectedIndex = null;
    widget.selectIndexCallback(selectedIndex);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: chartContainerAspectRatio,
      child: Stack(
        children: [
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  tooltipData = null;
                  selectedIndex = null;
                  widget.onTooltipHide?.call();
                });
              },
              child: Container(color: Colors.transparent),
            ),
          ),
          if (tooltipData != null) buildCustomTooltip(tooltipData!, Get.theme.primaryColor, chartContainerPadding),
          _buildChartSection(Get.theme.primaryColor),
        ],
      ),
    );
  }

  // 构建单个图表区域
  Widget _buildChartSection(Color chartColor) {
    return Padding(
      padding: chartContainerPadding,
      child: Align(
        alignment: Alignment.bottomCenter,
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          transitionBuilder: (Widget child, Animation<double> animation) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          child: isLoading
              ? const AspectRatio(key: ValueKey('loading'), aspectRatio: chartAspectRatio, child: DefaultBarChart())
              : pageDurationData.isEmpty
                  ? AspectRatio(
                      key: const ValueKey('no_data'),
                      aspectRatio: chartAspectRatio,
                      child: Center(
                        child: Text(
                          "暂无数据",
                          style: TextStyle(
                            color: const Color(0xff1C1A1A).withValues(alpha: 0.4),
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    )
                  : AspectRatio(
                      key: ValueKey('chart_${pageDurationData.length}'),
                      aspectRatio: chartAspectRatio,
                      child: Stack(
                        children: [
                          BarChart(
                            BarChartData(
                              backgroundColor: Colors.transparent,
                              alignment: BarChartAlignment.spaceBetween,
                              minY: widget.adjustMinY,
                              maxY: widget.adjustMaxY,
                              barTouchData: BarTouchData(
                                touchTooltipData: BarTouchTooltipData(
                                  // 禁用默认 tooltip
                                  getTooltipColor: (_) => Colors.transparent,
                                  getTooltipItem: (group, groupIndex, rod, rodIndex) {
                                    return BarTooltipItem('', const TextStyle());
                                  },
                                ),
                                touchCallback: (FlTouchEvent event, barTouchResponse) {
                                  _handleBarTouch(event, barTouchResponse);
                                },
                                // 增加触摸区域
                                touchExtraThreshold: const EdgeInsets.symmetric(horizontal: 10, vertical: 50),
                              ),
                              titlesData: FlTitlesData(
                                show: true,
                                rightTitles: const AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                ),
                                topTitles: const AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                ),
                                bottomTitles: AxisTitles(
                                  sideTitles: SideTitles(
                                    showTitles: true,
                                    reservedSize: bottomeservedSize,
                                    getTitlesWidget: (double value, TitleMeta meta) {
                                      if (value.toInt() >= 0 && value.toInt() < pageDurationData.length) {
                                        // 根据页面索引计算实际的LS编号
                                        final actualLsNumber = widget.currentPage * widget.pageSize + value.toInt() + 1;
                                        return SideTitleWidget(
                                          meta: meta,
                                          space: 8,
                                          child: Text(
                                            "$actualLsNumber LS",
                                            style: TextStyle(color: Colors.black.withValues(alpha: 0.6), fontSize: 10),
                                          ),
                                        );
                                      }
                                      return const SizedBox.shrink();
                                    },
                                  ),
                                ),
                                leftTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                              ),
                              borderData: FlBorderData(show: false),
                              barGroups: pageDurationData.asMap().entries.map((entry) {
                                final index = entry.key;
                                final durationChartData = entry.value;
                                final scoreChartData = pageScoreData[index];
                                final durationValue = (durationChartData.y ?? 0).toDouble();
                                final scoreValue = (scoreChartData.y ?? 0).toDouble();

                                return BarChartGroupData(
                                  x: index,
                                  barRods: [
                                    // 时长柱状图
                                    BarChartRodData(
                                      toY: durationValue,
                                      color: durationColor,
                                      width: barWidth,
                                      borderRadius: const BorderRadius.all(Radius.circular(12)),
                                      backDrawRodData: BackgroundBarChartRodData(show: false),
                                    ),
                                    // 分值柱状图
                                    BarChartRodData(
                                      toY: scoreValue,
                                      color: chartColor,
                                      width: barWidth,
                                      borderRadius: const BorderRadius.all(Radius.circular(12)),
                                      backDrawRodData: BackgroundBarChartRodData(show: false),
                                    ),
                                  ],
                                );
                              }).toList(),
                              gridData: const FlGridData(show: false, drawVerticalLine: false, drawHorizontalLine: false),
                            ),
                          ),
                        ],
                      ),
                    ),
        ),
      ),
    );
  }

  // 处理柱状图触摸事件
  void _handleBarTouch(FlTouchEvent event, BarTouchResponse? barTouchResponse) {
    // 处理所有类型的触摸事件
    if (event is FlTapUpEvent || event is FlTapDownEvent) {
      if (barTouchResponse != null && barTouchResponse.spot != null) {
        // 点击了柱状图
        final touchedIndex = barTouchResponse.spot!.touchedBarGroupIndex;
        final touchedRodDataIndex = barTouchResponse.spot!.touchedRodDataIndex;

        if (touchedIndex >= 0 && touchedIndex < pageDurationData.length) {
          final durationValue = pageDurationData[touchedIndex].y?.toDouble() ?? 0.0;
          final scoreValue = pageScoreData[touchedIndex].y?.toDouble() ?? 0.0;

          // 检查是否有有效数据
          if (durationValue > 0 || scoreValue > 0) {
            // 使用 BarTouchedSpot 的坐标信息
            final touchedSpot = barTouchResponse.spot!;
            final localOffset = touchedSpot.offset; // Offset，包含屏幕上的坐标

            final centerX = touchedRodDataIndex == 0 ? localOffset.dx + barWidth / 2 : localOffset.dx - barWidth / 2;

            // 使用较大的值作为回调值
            final value = durationValue > scoreValue ? durationValue : scoreValue;

            // 统一日期文本（用于各图表 tooltip 展示）
            final startIndex = widget.currentPage * widget.pageSize;
            final episodeData = widget.episodeLsDataList[startIndex + touchedIndex];
            final unifiedDateText = episodeData.finishTime != null && episodeData.finishTime! > 0
                ? '${DateTime.fromMillisecondsSinceEpoch(episodeData.finishTime!).year}年${DateTime.fromMillisecondsSinceEpoch(episodeData.finishTime!).month}月${DateTime.fromMillisecondsSinceEpoch(episodeData.finishTime!).day}号'
                : "";

            setState(() {
              final chartHeight = getChartHeight(chartContainerPadding);
              final barBottomY = chartHeight - bottomeservedSize;

              // 计算柱状图顶部的Y坐标
              final consumeBarTopY =
                  calculateChartBarTopY(touchedIndex, pageDurationData, barBottomY, chartHeight, widget.adjustMaxY, bottomeservedSize);

              tooltipData = CustomTooltipData(
                centerX: centerX,
                barTopY: consumeBarTopY,
                valueText: unifiedDateText,
                dateText: "",
                dateTextSpan: TextSpan(
                  children: [
                    formatMillisecondsSpan(
                      Duration(minutes: durationValue.toInt()).inMilliseconds,
                      numFontSize: 26,
                      numColor: Colors.white,
                      unitColor: Colors.white,
                    ),
                    TextSpan(
                      children: [
                        TextSpan(
                          text: " ${scoreValue.toStringAsFixed(1)}",
                          style: const TextStyle(fontSize: 26, fontWeight: FontWeight.bold, color: Colors.white),
                        ),
                        const TextSpan(
                          text: " avg",
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
              // 设置所有图表的 tooltip 和选中索引
              selectedIndex = touchedIndex;
            });

            // 调用 tooltip 显示回调
            widget.onTooltipShow?.call(touchedIndex, value);
          } else {
            hideAllTooltips();
          }
        } else {
          hideAllTooltips();
        }
      } else {
        hideAllTooltips();
      }
    }
  }

  // 添加关闭 tooltips 的方法
  void hideAllTooltips() {
    if (mounted) {
      setState(() {
        tooltipData = null;
        selectedIndex = null;
      });
      // 调用 tooltip 隐藏回调
      widget.onTooltipHide?.call();
    }
  }
}

// 默认柱状图组件
class DefaultBarChart extends StatelessWidget {
  const DefaultBarChart({super.key});

  @override
  Widget build(BuildContext context) {
    return BarChart(
      BarChartData(
        backgroundColor: Colors.transparent,
        alignment: BarChartAlignment.spaceBetween,
        minY: 0,
        maxY: 100,
        barTouchData: const BarTouchData(enabled: false),
        titlesData: const FlTitlesData(show: false),
        borderData: FlBorderData(show: false),
        barGroups: [],
        gridData: const FlGridData(show: false),
      ),
    );
  }
}
