import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';

import 'package:lsenglish/utils/time.dart';

import 'episode_chart_page.dart';
import '../controllers/episodedata_controller.dart';
import '../widgets/adjust_ls_times_sheet.dart';

class EpisodedataView extends GetView<EpisodedataController> {
  const EpisodedataView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(controller.episodeName.value)),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Container(
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  children: [
                    Stack(
                      children: [
                        Obx(() => AnimatedSwitcher(
                              duration: const Duration(milliseconds: 300),
                              child: controller.showDataSummary.value
                                  ? Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: _buildGrid(),
                                    )
                                  : const SizedBox.shrink(),
                            )),
                        Obx(() => TotalChartPageWidget(
                              // ignore: invalid_use_of_protected_member
                              episodeLsDataList: controller.episodeLsDataList.value,
                              pageIndexCallback: (pageIndex) {
                                controller.showDataSummary.value = true;
                              },
                              onTooltipShow: (i, v) {
                                controller.showDataSummary.value = false;
                              },
                              onTooltipHide: () {
                                controller.showDataSummary.value = true;
                              },
                            )),
                      ],
                    ),

                    // 添加分值和时间标签
                    const Gap(16),
                    Center(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              border: Border.all(color: const Color(0xFFE0E0E0), width: 1),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  width: 12,
                                  height: 12,
                                  decoration: const BoxDecoration(
                                    color: Colors.green,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const Gap(6),
                                Text(
                                  "分值",
                                  style: Get.textTheme.titleMedium,
                                ),
                              ],
                            ),
                          ),
                          const Gap(12),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              border: Border.all(color: const Color(0xFFE0E0E0), width: 1),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  width: 12,
                                  height: 12,
                                  decoration: const BoxDecoration(
                                    color: Colors.blue,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const Gap(6),
                                Text(
                                  "时间",
                                  style: Get.textTheme.titleMedium,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Gap(16),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Container(
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Gap(16),
                    _buildTableHeader(),
                    const Gap(8),
                    _buildTableContent(),
                    const Gap(16),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildGrid() {
    return Builder(
        builder: (context) => Row(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "总学习时长",
                      style: TextStyle(fontSize: 14, color: Color(0xFF625B71)),
                    ),
                    const Gap(8),
                    Obx(
                      () => RichText(
                        text: formatMillisecondsSpan(controller.episodeResp.value.totalLearnDuration, numFontSize: 26),
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "平均分值",
                      style: TextStyle(fontSize: 14, color: Color(0xFF625B71)),
                    ),
                    const Gap(8),
                    Obx(
                      () => RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: "${controller.episodeResp.value.averageScore ?? 0}",
                              style: const TextStyle(fontSize: 26, fontWeight: FontWeight.bold, color: Colors.black),
                            ),
                            const TextSpan(
                              text: " avg",
                              style: TextStyle(
                                fontSize: 16,
                                color: Color(0xFF999999),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "完成遍数",
                      style: TextStyle(fontSize: 14, color: Color(0xFF625B71)),
                    ),
                    const Gap(8),
                    GestureDetector(
                      onTap: () {
                        AdjustLsTimesSheet.show(
                          context,
                          currentLsTimes: controller.episodeResp.value.currentLsTimes ?? 0,
                          targetLsTimes: controller.episodeResp.value.targetLsTimes ?? 21,
                          resourceId: controller.resourceId,
                          resourceType: controller.resourceType,
                          onLsTimesUpdated: (newTargetLsTimes) {
                            // 更新本地数据
                            controller.episodeResp.update((val) {
                              val?.targetLsTimes = newTargetLsTimes;
                            });
                          },
                        );
                      },
                      child: Obx(
                        () => RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: "${controller.episodeResp.value.currentLsTimes ?? 0}",
                                style: const TextStyle(
                                  fontSize: 26,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                              ),
                              const TextSpan(
                                text: "nd",
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF999999),
                                ),
                              ),
                              TextSpan(
                                text: " /${controller.episodeResp.value.targetLsTimes} time",
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF999999),
                                  decoration: TextDecoration.underline,
                                  decorationColor: Color(0xFFCCCCCC),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ));
  }

  Widget _buildTableHeader() {
    const headerStyle = TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: Color(0xFF625B71),
    );

    return const Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(child: Text("第几遍", textAlign: TextAlign.center, style: headerStyle)),
        Expanded(child: Text("平均分值", textAlign: TextAlign.center, style: headerStyle)),
        Expanded(child: Text("所用时长", textAlign: TextAlign.center, style: headerStyle)),
        Expanded(child: Text("完成时间", textAlign: TextAlign.center, style: headerStyle)),
      ],
    );
  }

  Widget _buildTableContent() {
    return Obx(() {
      if (controller.episodeLsDataList.isEmpty) {
        return Container(
          padding: const EdgeInsets.all(32),
          child: const Center(
            child: Column(
              children: [
                Icon(
                  Icons.table_chart_outlined,
                  size: 48,
                  color: Color(0xFFCCCCCC),
                ),
                Gap(16),
                Text(
                  "暂无学习数据",
                  style: TextStyle(
                    fontSize: 16,
                    color: Color(0xFF999999),
                  ),
                ),
              ],
            ),
          ),
        );
      }

      return ListView.builder(
        itemCount: controller.episodeLsDataList.length,
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemBuilder: (BuildContext context, int index) {
          return _buildTableRow(index);
        },
      );
    });
  }

  Widget _buildTableRow(int index) {
    const contentStyle = TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      color: Color(0xFF2C3E50),
    );

    return Padding(
      padding: const EdgeInsets.only(top: 16, bottom: 16, left: 16, right: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              "${controller.episodeLsDataList[index].lsTimes ?? 0} time",
              style: contentStyle,
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            child: Text(
              controller.episodeLsDataList[index].averageScore?.toStringAsFixed(2) ?? "0",
              style: contentStyle,
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            child: RichText(
              text: formatMillisecondsSpan(controller.episodeLsDataList[index].totalLearnDuration, numFontSize: 16),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            child: index == 0
                ? Center(
                    child: ElevatedButton(
                      onPressed: () {
                        controller.showLsTimeDialog();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Get.theme.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text("完成", style: TextStyle(fontSize: 12)),
                    ),
                  )
                : Text(
                    controller.episodeLsDataList[index].finishTime == 0
                        ? "--"
                        : DateFormat('yyyy/MM/dd').format(DateTime.fromMillisecondsSinceEpoch(controller.episodeLsDataList[index].finishTime!)),
                    style: contentStyle,
                    textAlign: TextAlign.center,
                  ),
          ),
        ],
      ),
    );
  }
}
