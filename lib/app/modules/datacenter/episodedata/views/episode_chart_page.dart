import 'package:flutter/material.dart';
import 'package:lsenglish/model/episode_ls_data_resp/episode_ls_data.dart';
import 'package:lsenglish/utils/chart.dart';
import 'package:lsenglish/utils/time.dart';

import 'episode_chart.dart';

typedef PageIndexCallback = void Function(int);

class TotalChartPageWidget extends StatefulWidget {
  final List<EpisodeLsData> episodeLsDataList;
  final PageIndexCallback pageIndexCallback;
  final Function(int index, double value)? onTooltipShow; // tooltip 显示回调
  final int pageSize;
  final VoidCallback? onTooltipHide; // tooltip 隐藏回调
  const TotalChartPageWidget({
    super.key,
    required this.episodeLsDataList,
    required this.pageIndexCallback,
    this.onTooltipShow,
    this.onTooltipHide,
    this.pageSize = 7,
  });

  @override
  State<TotalChartPageWidget> createState() => TotalChartPageWidgetState();
}

class TotalChartPageWidgetState extends State<TotalChartPageWidget> {
  int currentPageIndex = 0;
  var pageKey = GlobalKey();
  PageController pageController = PageController();
  final Map<int, GlobalKey<EpisodeChartWidgetState>> chartWidgetKeys = {};
  var adjustMinY = 0.0;
  var adjustMaxY = 100.0;
  int pageCount = 1;

  @override
  void initState() {
    super.initState();
    _calculateYAxisRange();
  }

  @override
  void didUpdateWidget(covariant TotalChartPageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.episodeLsDataList != widget.episodeLsDataList) {
      _calculateYAxisRange();
    }
  }

  void _calculateYAxisRange() {
    var episodeLsDataList = widget.episodeLsDataList;
    episodeLsDataList.sort((a, b) => a.lsTimes!.compareTo(b.lsTimes!));
    pageCount = (episodeLsDataList.length / widget.pageSize).ceil();

    if (episodeLsDataList.isEmpty) {
      return;
    }

    // 直接从 episodeLsDataList 计算最大值
    var maxDuration = 0.0;
    var maxScore = 0.0;

    for (var episodeData in episodeLsDataList) {
      // 计算时长最大值
      if (episodeData.totalLearnDuration != null) {
        final duration = millisecondsToMinutes(episodeData.totalLearnDuration!);
        if (duration > maxDuration) {
          maxDuration = duration;
        }
      }

      // 计算分值最大值
      if (episodeData.averageScore != null) {
        final score = episodeData.averageScore!.toDouble();
        if (score > maxScore) {
          maxScore = score;
        }
      }
    }

    // 取两者中的较大值作为Y轴最大值
    var maxY = maxDuration > maxScore ? maxDuration : maxScore;

    var adjustYValues = calculateYAxisLabels(maxY);
    setState(() {
      adjustMinY = 0;
      adjustMaxY = adjustYValues;
    });
  }

  void hideAllTooltips() {
    for (var key in chartWidgetKeys.keys) {
      chartWidgetKeys[key]?.currentState?.hideTooltips();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: chartContainerAspectRatio,
      child: PageView.builder(
        key: pageKey,
        itemCount: pageCount,
        controller: pageController,
        onPageChanged: (value) {
          currentPageIndex = value;
          widget.pageIndexCallback(value);
          hideAllTooltips();
        },
        itemBuilder: (BuildContext context, int index) {
          if (!chartWidgetKeys.containsKey(index)) {
            chartWidgetKeys[index] = GlobalKey<EpisodeChartWidgetState>();
          }
          return EpisodeChartWidget(
            key: chartWidgetKeys[index]!,
            episodeLsDataList: widget.episodeLsDataList,
            currentPage: index,
            pageSize: widget.pageSize,
            adjustMinY: adjustMinY,
            adjustMaxY: adjustMaxY,
            onTooltipShow: widget.onTooltipShow,
            onTooltipHide: widget.onTooltipHide,
            selectIndexCallback: (int? index) {
              // 可以在这里处理选中索引的回调
            },
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }
}
