import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';
import 'package:lsenglish/widgets/loading_button.dart';
import 'package:lsenglish/net/net.dart';

class AdjustLsTimesSheet {
  /// 显示调整LS次数弹窗
  static void show(
    BuildContext context, {
    required int currentLsTimes,
    required int targetLsTimes,
    required String resourceId,
    required int resourceType,
    Function(int newTargetLsTimes)? onLsTimesUpdated,
  }) {
    WoltModalSheet.show<void>(
      context: context,
      pageListBuilder: (BuildContext context) {
        return [
          WoltModalSheetPage(
            id: 'adjustLsTimes',
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            trailingNavBarWidget: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close),
            ),
            leadingNavBarWidget: Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Text(
                '调整目标LS次数',
                style: Get.textTheme.headlineMedium,
              ),
            ),
            child: _buildAdjustLsTimesContent(
              currentLsTimes: currentLsTimes,
              targetLsTimes: targetLsTimes,
              resourceId: resourceId,
              resourceType: resourceType,
              onLsTimesUpdated: onLsTimesUpdated,
            ),
          ),
        ];
      },
    );
  }

  /// 构建调整LS次数页面内容
  static Widget _buildAdjustLsTimesContent({
    required int currentLsTimes,
    required int targetLsTimes,
    required String resourceId,
    required int resourceType,
    Function(int newTargetLsTimes)? onLsTimesUpdated,
  }) {
    final selectedTargetLsTimes = targetLsTimes.obs;

    return Builder(
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 当前信息显示
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.grey[300]!,
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '当前信息',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[700],
                      ),
                    ),
                    const Gap(8),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '已完成次数',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                              Text(
                                '$currentLsTimes 次',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '目标次数',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                              Obx(() => Text(
                                    '${selectedTargetLsTimes.value} 次',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  )),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const Gap(24),
              
              // 拖动条
              Obx(() => Column(
                    children: [
                      SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          activeTrackColor: Get.theme.primaryColor,
                          inactiveTrackColor: Colors.grey[300],
                          thumbColor: Get.theme.primaryColor,
                          overlayColor: Get.theme.primaryColor.withOpacity(0.2),
                          trackHeight: 4,
                          thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                          overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
                        ),
                        child: Slider(
                          value: selectedTargetLsTimes.value.toDouble().clamp(currentLsTimes.toDouble(), 100.0),
                          min: currentLsTimes.toDouble(),
                          max: 100.0,
                          divisions: 100 - currentLsTimes,
                          onChanged: (value) {
                            selectedTargetLsTimes.value = value.round();
                          },
                        ),
                      ),
                      const Gap(10),
                      // 拖动条刻度标签
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${currentLsTimes}次',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            '100次',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  )),
              const Gap(20),
              
              // 提示信息
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.blue[200]!,
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.blue[600],
                      size: 20,
                    ),
                    const Gap(8),
                    Expanded(
                      child: Text(
                        '目标次数不能低于当前已完成次数。建议根据学习进度合理设置目标。',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const Gap(30),
              
              // 确认按钮
              LoadingButton.text(
                text: '确认修改',
                buttonColor: Get.theme.primaryColor,
                onAsyncPressed: () async {
                  // 调用更新目标LS次数接口
                  final success = await _updateTargetLsTimes(
                    selectedTargetLsTimes.value,
                    resourceId: resourceId,
                    resourceType: resourceType,
                  );
                  if (success) {
                    Navigator.of(context).pop();
                    onLsTimesUpdated?.call(selectedTargetLsTimes.value);
                  }
                  return success;
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// 更新目标LS次数接口调用
  static Future<bool> _updateTargetLsTimes(
    int targetLsTimes, {
    required String resourceId,
    required int resourceType,
  }) async {
    try {
      // 使用现有的 modDataEpisode 接口，传入 targetLsTimes 参数
      final response = await Net.getRestClient().updateDataEpisode({
        'resourceId': resourceId,
        'resourceType': resourceType,
        'targetLsTimes': targetLsTimes,
      });

      if (response.data != null) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }
}
