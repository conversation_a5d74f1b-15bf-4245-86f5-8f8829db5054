import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';
import 'package:lsenglish/widgets/loading_button.dart';
import 'package:lsenglish/net/net.dart';

class LsTimesSheet {
  /// 显示LS次数调整弹窗
  static void show(
    BuildContext context, {
    required String resourceId,
    required int resourceType,
    String? episodeName,
    int? currentLsTimes,
    int? targetLsTimes,
    VoidCallback? onLsTimesUpdated,
  }) {
    WoltModalSheet.show<void>(
      context: context,
      pageListBuilder: (BuildContext context) {
        return [
          WoltModalSheetPage(
            id: 'lsTimes',
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            trailingNavBarWidget: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close),
            ),
            leadingNavBarWidget: Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Text(
                'LS学习遍数',
                style: Get.textTheme.headlineMedium,
              ),
            ),
            child: _buildLsTimesContent(
              resourceId: resourceId,
              resourceType: resourceType,
              episodeName: episodeName,
              currentLsTimes: currentLsTimes,
              targetLsTimes: targetLsTimes,
              onLsTimesUpdated: onLsTimesUpdated,
            ),
          ),
        ];
      },
    );
  }

  /// 构建LS次数页面内容
  static Widget _buildLsTimesContent({
    required String resourceId,
    required int resourceType,
    String? episodeName,
    int? currentLsTimes,
    int? targetLsTimes,
    VoidCallback? onLsTimesUpdated,
  }) {
    return Builder(
      builder: (context) {
        final currentLs = currentLsTimes ?? 0;
        final targetLs = targetLsTimes ?? 0;
        final episodeNameValue = episodeName ?? '';

        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 剧集名称
              Text(
                episodeNameValue,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
              const Gap(16),

              // LS学习遍数标题
              const Text(
                "LS学习遍数",
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Gap(24),

              // 当前信息显示
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.grey[300]!,
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '+ ',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          '1',
                          style: TextStyle(
                            fontSize: 36,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const Gap(8),
                    Text(
                      '当前已完成: $currentLs 次',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    if (targetLs > 0) ...[
                      const Gap(4),
                      Text(
                        '目标次数: $targetLs 次',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              const Gap(30),

              // 完成按钮
              LoadingButton.text(
                text: '完成',
                buttonColor: Get.theme.primaryColor,
                onAsyncPressed: () async {
                  final success = await _updateLsTimes(
                    resourceId: resourceId,
                    resourceType: resourceType,
                    currentLsTimes: currentLs,
                  );
                  if (success) {
                    Navigator.of(context).pop();
                    onLsTimesUpdated?.call();
                  }
                  return success;
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// 更新LS次数接口调用
  static Future<bool> _updateLsTimes({
    required String resourceId,
    required int resourceType,
    required int currentLsTimes,
  }) async {
    try {
      final updateResponse = await Net.getRestClient().updateDataEpisode({
        'resourceId': resourceId,
        'resourceType': resourceType,
        'currentLsTimes': currentLsTimes + 1,
      });
      return updateResponse.isSuccess;
    } catch (e) {
      return false;
    }
  }
}
