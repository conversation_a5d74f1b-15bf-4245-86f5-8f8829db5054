import 'package:get/get.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/obs.dart';

import '../../../../../model/data_center_home_resp/episode.dart';
import '../../../../../model/episode_ls_data_resp/episode_ls_data.dart';
import '../widgets/ls_times_sheet.dart';

class EpisodedataController extends GetxController {
  var resourceId = '';
  var resourceType = 1;
  var episodeName = ''.obs;
  var episodeLsDataList = <EpisodeLsData>[].obs;
  var episodeResp = Episode().obs;
  var showDataSummary = true.obs;
  @override
  void onInit() {
    super.onInit();
    resourceId = Get.arguments?['resourceId'] ?? "";
    resourceType = Get.arguments?['resourceType'] ?? 1;
    episodeName.value = Get.arguments?['episodeName'] ?? '';
    fetchData();
  }

  void fetchData() {
    Net.getRestClient().dataEpisodeLsData(resourceId, resourceType).then((v) {
      episodeLsDataList.value = v.data.episodeLsData ?? [];
      episodeResp.value = v.data.episode ?? Episode();
    });
  }

  void showLsTimeDialog() async {
    LsTimesSheet.show(
      Get.context!,
      resourceId: resourceId,
      resourceType: resourceType,
      currentLsTimes: episodeResp.value.currentLsTimes ?? 1,
      targetLsTimes: episodeResp.value.targetLsTimes ?? 1,
      episodeName: episodeName.value,
      onLsTimesUpdated: () {
        fetchData();
        ObsUtil().updateDataCenter.value = DateTime.now().millisecond;
      },
    );
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
