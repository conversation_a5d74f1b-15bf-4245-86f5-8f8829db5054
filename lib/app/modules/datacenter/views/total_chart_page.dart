import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/model/data_center_chart_resp/data_center_chart_resp.dart';

import 'package:lsenglish/utils/time.dart';

import '../../../../utils/chart.dart';
import 'total_chart.dart';

typedef PageIndexCallback = void Function(int);

class TotalChartPageWidget extends StatefulWidget {
  final int tabType;
  final PageIndexCallback pageIndexCallback;
  const TotalChartPageWidget({super.key, required this.tabType, required this.pageIndexCallback});

  @override
  State<TotalChartPageWidget> createState() => TotalChartPageWidgetState();
}

class TotalChartPageWidgetState extends State<TotalChartPageWidget> {
  int? selectedIndex;
  int? pageIndex;
  int currentPageIndex = 0;
  DataCenterChartResp? dataCenterChartResp;
  var pageKey = GlobalKey();
  String chartTimeRangeString = "";
  // 数据卡片显示状态控制
  bool _showDataCards = true;
  PageController pageController = PageController();
  final Map<int, GlobalKey<TotalChartWidgetState>> chartWidgetKeys = {};

  @override
  void initState() {
    super.initState();
    chartTimeRangeString = getChartTimeRangeString(widget.tabType, currentPageIndex);
  }

  @override
  void didUpdateWidget(covariant TotalChartPageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.tabType != widget.tabType) {
      debugPrint("widget.tabType = ${widget.tabType}");
      setState(() {
        pageKey = GlobalKey();
        chartTimeRangeString = getChartTimeRangeString(widget.tabType, currentPageIndex);
        _showDataCards = true; // 重置数据卡片显示状态
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  void refreshLastPageData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      pageController.animateToPage(0, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut).then(
        (value) {
          if (mounted) {
            (chartWidgetKeys[0]?.currentState)?.refreshData();
          }
        },
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: chartContainerAspectRatio,
      child: Container(
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Stack(
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return FadeTransition(
                  opacity: animation,
                  child: child,
                );
              },
              child: _showDataCards
                  ? Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                      child: AnimatedOpacity(
                        opacity: pageIndex == currentPageIndex ? (selectedIndex == null ? 1 : 0) : 1,
                        duration: const Duration(milliseconds: 100),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "总时长",
                              style: Get.textTheme.titleLarge,
                            ),
                            RichText(
                              text: formatMillisecondsSpan(dataCenterChartResp?.totalLearnDuration),
                            ),
                            Text(chartTimeRangeString),
                          ],
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
            PageView.builder(
              key: pageKey,
              reverse: true,
              controller: pageController,
              onPageChanged: (value) {
                currentPageIndex = value;
                widget.pageIndexCallback(value);
                setState(() {
                  chartTimeRangeString = getChartTimeRangeString(widget.tabType, currentPageIndex);
                  _showDataCards = true;
                });
              },
              itemBuilder: (BuildContext context, int index) {
                if (!chartWidgetKeys.containsKey(index)) {
                  chartWidgetKeys[index] = GlobalKey<TotalChartWidgetState>();
                }
                return TotalChartWidget(
                  type: widget.tabType,
                  key: chartWidgetKeys[index]!,
                  selectIndexCallback: (pi, si) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      setState(() {
                        selectedIndex = si;
                        pageIndex = pi;
                      });
                    });
                  },
                  chartDataCallback: (data) {
                    setState(() {
                      dataCenterChartResp = data;
                    });
                  },
                  onTooltipShow: (index, chartType, value) {
                    debugPrint('onTooltipShow: $index, $chartType, $value');
                    setState(() {
                      _showDataCards = false;
                    });
                  },
                  onTooltipHide: () {
                    debugPrint('onTooltipHide');
                    setState(() {
                      _showDataCards = true;
                    });
                  },
                  pageIndex: index,
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
