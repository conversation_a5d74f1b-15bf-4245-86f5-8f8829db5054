import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';

import 'package:lsenglish/base/loading_getview.dart';
import 'package:lsenglish/utils/dialog.dart';

import 'package:lsenglish/utils/time.dart';
import 'package:lsenglish/widgets/tab_indicator_styler/src/indicators/rectangular_indicator.dart';

import '../controllers/datacenter_controller.dart';
import 'total_chart_page.dart';

class DatacenterView extends LoadingGetView<DatacenterController> {
  const DatacenterView({super.key});
  @override
  Widget buildSuccess(BuildContext context) {
    return Scaffold(
      backgroundColor: Get.theme.appBarTheme.surfaceTintColor,
      appBar: AppBar(title: const Text("学习数据")),
      body: SafeArea(
        bottom: false,
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFFEEEEEE).withValues(alpha: 0.5),
                        borderRadius: const BorderRadius.all(Radius.circular(8)),
                      ),
                      child: TabBar(
                        controller: controller.tabController,
                        tabs: ["日", "周", "月", "年"]
                            .map((label) => Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 10),
                                  child: Text(label, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Color(0xff15161A))),
                                ))
                            .toList(),
                        labelColor: Colors.black,
                        labelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Color(0xff15161A)),
                        unselectedLabelStyle:
                            TextStyle(fontSize: 12, fontWeight: FontWeight.w500, color: const Color(0xff15161A).withValues(alpha: 0.5)),
                        unselectedLabelColor: const Color(0xff15161A).withValues(alpha: 0.5),
                        dividerColor: Colors.transparent,
                        indicatorSize: TabBarIndicatorSize.tab,
                        indicator: RectangularIndicator(
                          verticalPadding: 4,
                          horizontalPadding: 4,
                          radius: 20,
                          strokeWidth: 0,
                          color: const Color(0xffDFE3E8),
                          selectBackgroundColor: Colors.white,
                          paintingStyle: PaintingStyle.stroke,
                          shadowColor: Colors.black.withValues(alpha: 0.1),
                          shadowOffset: const Offset(0, 2),
                          shadowBlurRadius: 4,
                          shadowSpreadRadius: 0,
                        ),
                      ),
                    ),
                    const Gap(10),
                    Obx(() => TotalChartPageWidget(
                          tabType: controller.tabType.value,
                          pageIndexCallback: (index) {
                            controller.onChartPageIndexChange(index);
                          },
                          key: controller.chartPageKey,
                        )),
                  ],
                ),
                const Gap(16),
                Text("学习", style: Get.textTheme.headlineLarge),
                const Gap(16),
                Obx(() => Visibility(visible: controller.dataCenterHomeResp.value.episodes != null, child: _buildTrend())),
                Obx(() => Visibility(visible: controller.dataCenterHomeResp.value.total != null, child: _buildTotal())),
                const Gap(40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTrend() {
    return Obx(
      () => ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: controller.dataCenterHomeResp.value.episodes?.length ?? 0,
        itemBuilder: (BuildContext context, int index) {
          final episode = controller.dataCenterHomeResp.value.episodes?[index];

          return GestureDetector(
            onTap: () {
              Get.toNamed(Routes.EPISODEDATA, arguments: {
                'resourceId': episode?.resourceId,
                'resourceType': episode?.resourceType,
                'episodeName': episode?.episodeName,
              });
            },
            child: Padding(
              padding:
                  EdgeInsets.only(top: index == 0 ? 0 : 6, bottom: index == (controller.dataCenterHomeResp.value.episodes?.length ?? 0) - 1 ? 12 : 6),
              child: Container(
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              showChangeResourceNameDialog(episode?.resourceId, episode?.resourceType, episode?.episodeName);
                            },
                            child: Text(
                              episode?.episodeName ?? "",
                              style: Get.textTheme.titleLarge,
                            ),
                          ),
                        ),
                        const Icon(Icons.arrow_forward_ios_rounded, size: 24, color: Colors.grey)
                      ],
                    ),
                    const Gap(16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 句子数量
                        Flexible(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                (episode?.currentSentences ?? 0).toString(),
                                style: TextStyle(
                                    fontSize: 24,
                                    color: (episode?.currentSentences ?? 0) > (episode?.targetSentences ?? 0) ? Get.theme.primaryColor : Colors.black,
                                    fontWeight: FontWeight.bold),
                              ),
                              Text(
                                "${episode?.targetSentences.toString() ?? "--"} sent.",
                                style: const TextStyle(fontSize: 12, color: Colors.grey, decoration: TextDecoration.underline),
                              ),
                            ],
                          ),
                        ),
                        // 平均分数
                        Flexible(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                episode?.averageScore?.toStringAsFixed(0) ?? "--",
                                style: const TextStyle(fontSize: 24, color: Colors.black, fontWeight: FontWeight.bold),
                              ),
                              const Text(
                                "avg.score",
                                style: TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                        // 分钟数
                        Flexible(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                millisecondsToMinutesString(episode?.totalLearnDuration ?? 0, precision: 0),
                                style: const TextStyle(fontSize: 24, color: Colors.black, fontWeight: FontWeight.bold),
                              ),
                              const Text(
                                "min",
                                style: TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                        // LS值
                        Flexible(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                episode?.currentLsTimes.toString() ?? "--",
                                style: const TextStyle(fontSize: 24, color: Colors.black, fontWeight: FontWeight.bold),
                              ),
                              Text(
                                "${episode?.targetLsTimes.toString() ?? "--"} LS",
                                style: const TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTotal() {
    var title = ["学习总时长", "学习总天数", "学习视频"];
    var unit = ["", "d", "个"];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text("累计", style: Get.textTheme.headlineLarge),
        const Gap(12),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 3,
          itemBuilder: (BuildContext context, int index) {
            return Padding(
              padding: EdgeInsets.only(top: index == 0 ? 0 : 6, bottom: index == 1 ? 12 : 6),
              child: GestureDetector(
                onTap: () {
                  if (index == 2) {
                    Get.toNamed(Routes.EPISODELIST);
                  }
                },
                child: Container(
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            title[index],
                            style: TextStyle(fontSize: 18, color: Get.theme.primaryColor, fontWeight: FontWeight.bold),
                          ),
                          const Icon(Icons.arrow_forward_ios_rounded, size: 24, color: Colors.grey)
                        ],
                      ),
                      const Gap(32),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.baseline,
                        textBaseline: TextBaseline.alphabetic,
                        children: [
                          index == 0
                              ? Obx(
                                  () => RichText(
                                    text: formatMillisecondsSpan(controller.dataCenterHomeResp.value.total?.totalLearnDuration, numFontSize: 32),
                                  ),
                                )
                              : index == 1
                                  ? Obx(() => Text(
                                        controller.dataCenterHomeResp.value.total?.totalLearnDayTimes.toString() ?? "--",
                                        style: const TextStyle(fontSize: 32, color: Colors.black, fontWeight: FontWeight.bold),
                                      ))
                                  : Obx(() => Text(
                                        controller.dataCenterHomeResp.value.total?.totalLearnVideoSize.toString() ?? "--",
                                        style: const TextStyle(fontSize: 32, color: Colors.black, fontWeight: FontWeight.bold),
                                      )),
                          Text(
                            " ${unit[index]}",
                            style: const TextStyle(fontSize: 16, color: Colors.grey, fontWeight: FontWeight.bold),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
