import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/model/data_center_chart_resp/data_center_chart_resp.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/chart.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/time.dart';

typedef SelectIndexCallback = void Function(int, int?);
typedef ChartDataCallback = void Function(DataCenterChartResp?);

class TotalChartWidget extends StatefulWidget {
  final int type;
  final SelectIndexCallback selectIndexCallback;
  final ChartDataCallback chartDataCallback;
  final int pageIndex;
  final Function(int index, int chartType, double value)? onTooltipShow; // tooltip 显示回调
  final VoidCallback? onTooltipHide; // tooltip 隐藏回调
  const TotalChartWidget({
    super.key,
    required this.type,
    required this.pageIndex,
    required this.selectIndexCallback,
    required this.chartDataCallback,
    this.onTooltipShow, // 添加 tooltip 显示回调
    this.onTooltipHide, // 添加 tooltip 隐藏回调
  });

  @override
  State<TotalChartWidget> createState() => TotalChartWidgetState();
}

class TotalChartWidgetState extends State<TotalChartWidget> {
  int? touchedGroupIndex;
  var chartData = <ChartData>[];
  var adjustMinY = 0.0;
  var adjustMaxY = 100.0;
  int? selectedIndex;
  DataCenterChartResp? dataCenterChartResp;

  final chartContainerPadding = const EdgeInsets.only(top: 16, bottom: 16, left: 18, right: 14);
  final bottomeservedSize = 20.0;
  bool isLoading = false;
  CustomTooltipData? tooltipData;
  @override
  void initState() {
    super.initState();
    tooltipData = null;
    fetchChartData();
  }

  @override
  void dispose() {
    selectedIndex = null;
    widget.selectIndexCallback(widget.pageIndex, selectedIndex);
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant TotalChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.type != widget.type) {
      selectedIndex = null;
      widget.selectIndexCallback(widget.pageIndex, selectedIndex);
      fetchChartData();
    }
  }

  // 添加关闭 tooltips 的方法
  void hideTooltips() {
    if (mounted) {
      setState(() {
        tooltipData = null;
        selectedIndex = null;
      });
      // 调用 tooltip 隐藏回调
      widget.onTooltipHide?.call();
    }
  }

  void fetchChartData({bool needLoading = true}) {
    if (needLoading) {
      setState(() {
        isLoading = true;
      });
    }
    var timePair = getChartTimeRange(widget.type, widget.pageIndex);
    Net.getRestClient().dataEpisodeChart(widget.type, timePair.first ?? 0, timePair.second ?? 0).then((value) {
      if (value.data.datas == null || value.data.datas?.isEmpty == true) {
        return;
      }
      dataCenterChartResp = value.data;
      widget.chartDataCallback(value.data);
      var datas = value.data.datas!;
      var tempChartData = <ChartData>[];
      for (var i = 0; i < datas.length; i++) {
        tempChartData.add(ChartData(x: i, y: datas[i].duration != null ? millisecondsToMinutes(datas[i].duration!) : null));
      }
      chartData = tempChartData;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _handleAdjustY();
          isLoading = false;
        });
      });
    });
  }

  // 添加公开的刷新方法
  void refreshData() {
    fetchChartData(needLoading: false);
  }

  void _handleAdjustY() {
    var maxY = chartData.where((element) => element.y != null).map((element) => element.y!).reduce((current, next) => current > next ? current : next)
        as double;
    var adjustYValues = calculateYAxisLabels(maxY);
    setState(() {
      adjustMinY = 0;
      adjustMaxY = adjustYValues;
    });
    logger("adjustMinY = $adjustMinY   adjustMaxY = $adjustMaxY chartData size = ${chartData.length}");
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: chartContainerAspectRatio,
      child: Stack(
        children: [
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  tooltipData = null;
                  selectedIndex = null;
                  widget.onTooltipHide?.call();
                });
              },
              child: Container(color: Colors.transparent),
            ),
          ),
          if (tooltipData != null) buildCustomTooltip(tooltipData!, Get.theme.primaryColor, chartContainerPadding),
          _buildChartSection(chartData, Get.theme.primaryColor, widget.type),
        ],
      ),
    );
  }

  // 构建单个图表区域
  Widget _buildChartSection(List<ChartData> data, Color chartColor, int chartType) {
    bool hasValidData = data.any((element) => element.y != null && element.y != 0);

    return Padding(
      padding: chartContainerPadding,
      child: Align(
        alignment: Alignment.bottomCenter,
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          transitionBuilder: (Widget child, Animation<double> animation) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          child: isLoading
              ? const AspectRatio(key: ValueKey('loading'), aspectRatio: chartAspectRatio, child: DefaultBarChart())
              : data.isEmpty || !hasValidData
                  ? AspectRatio(
                      key: const ValueKey('no_data'),
                      aspectRatio: chartAspectRatio,
                      child: Center(
                        child: Text(
                          "No data",
                          style: TextStyle(
                            color: const Color(0xff1C1A1A).withOpacity(0.4),
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    )
                  : AspectRatio(
                      key: ValueKey('chart_${chartType}_${data.length}'),
                      aspectRatio: chartAspectRatio,
                      child: Stack(
                        children: [
                          BarChart(
                            BarChartData(
                              backgroundColor: Colors.transparent,
                              alignment: BarChartAlignment.spaceBetween,
                              minY: adjustMinY,
                              maxY: adjustMaxY,
                              barTouchData: BarTouchData(
                                touchTooltipData: BarTouchTooltipData(
                                  // 禁用默认 tooltip
                                  getTooltipColor: (_) => Colors.transparent,
                                  getTooltipItem: (group, groupIndex, rod, rodIndex) {
                                    return BarTooltipItem('', const TextStyle());
                                  },
                                ),
                                touchCallback: (FlTouchEvent event, barTouchResponse) {
                                  _handleBarTouch(event, barTouchResponse, chartType, data);
                                },
                                // 增加触摸区域
                                touchExtraThreshold: const EdgeInsets.symmetric(horizontal: 10, vertical: 50),
                              ),
                              titlesData: FlTitlesData(
                                show: true,
                                rightTitles: const AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                ),
                                topTitles: const AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                ),
                                bottomTitles: AxisTitles(
                                  sideTitles: SideTitles(
                                    showTitles: true,
                                    reservedSize: bottomeservedSize,
                                    getTitlesWidget: (double value, TitleMeta meta) {
                                      if (value.toInt() >= 0 && value.toInt() < data.length) {
                                        String labelText = '';

                                        switch (widget.type) {
                                          case 1:
                                            if (value.toInt() == 0 ||
                                                value.toInt() == 6 ||
                                                value.toInt() == 12.0 ||
                                                value.toInt() == 18.0 ||
                                                value.toInt() == 23.0) {
                                              labelText = "${value.toInt()}时";
                                            } else {
                                              labelText = "";
                                            }
                                            break;
                                          case 2:
                                            final weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];
                                            labelText = weekdays[value.toInt() % 7];
                                            break;
                                          case 3:
                                            // 月：只显示关键日期标签（1、5、10、15、20、25、30）
                                            final day = value.toInt() + 1;
                                            if ([1, 5, 10, 15, 20, 25, 30].contains(day)) {
                                              labelText = day.toString();
                                            } else {
                                              return const SizedBox.shrink(); // 不显示其他标签
                                            }
                                            break;
                                          case 4:
                                            final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                                            labelText = months[value.toInt()];
                                            break;
                                        }

                                        return SideTitleWidget(
                                          meta: meta,
                                          space: 8,
                                          child: Text(
                                            labelText,
                                            style: TextStyle(color: Colors.black.withOpacity(0.6), fontSize: 10),
                                          ),
                                        );
                                      }
                                      return const SizedBox.shrink();
                                    },
                                  ),
                                ),
                                leftTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                              ),
                              borderData: FlBorderData(show: false),
                              barGroups: data.asMap().entries.map((entry) {
                                final index = entry.key;
                                final chartData = entry.value;
                                final yValue = (chartData.y ?? 0).toDouble();

                                return BarChartGroupData(
                                  x: index,
                                  barRods: [
                                    BarChartRodData(
                                      toY: yValue,
                                      color: Get.theme.primaryColor,
                                      width: calculateBarWidth(data.length),
                                      borderRadius: const BorderRadius.all(Radius.circular(4)),
                                      backDrawRodData: BackgroundBarChartRodData(show: false),
                                    ),
                                  ],
                                );
                              }).toList(),
                              gridData: const FlGridData(show: false, drawVerticalLine: false, drawHorizontalLine: false),
                            ),
                          ),
                        ],
                      ),
                    ),
        ),
      ),
    );
  }

  // 处理柱状图触摸事件
  void _handleBarTouch(FlTouchEvent event, BarTouchResponse? barTouchResponse, int chartType, List<ChartData> data) {
    // 处理所有类型的触摸事件
    if (event is FlTapUpEvent || event is FlTapDownEvent) {
      if (barTouchResponse != null && barTouchResponse.spot != null) {
        // 点击了柱状图
        final touchedIndex = barTouchResponse.spot!.touchedBarGroupIndex;

        debugPrint('Chart touch: type=$chartType, index=$touchedIndex, data=${data[touchedIndex].y}');
        if (touchedIndex >= 0 && touchedIndex < data.length && data[touchedIndex].y != null && data[touchedIndex].y != 0) {
          // 使用 BarTouchedSpot 的坐标信息
          final touchedSpot = barTouchResponse.spot!;
          final chartSpot = touchedSpot.spot; // FlSpot，包含图表中的坐标
          final localOffset = touchedSpot.offset; // Offset，包含屏幕上的坐标

          debugPrint('Chart spot: x=${chartSpot.x}, y=${chartSpot.y}');
          debugPrint('Chart Local offset: x=${localOffset.dx}, y=${localOffset.dy}');

          // 直接使用图表中的实际坐标
          final chartX = chartSpot.x; // 图表中的 X 坐标
          final chartY = chartSpot.y; // 图表中的 Y 坐标

          debugPrint('Chart coordinates: x=$chartX, y=$chartY');

          // 统一日期文本（用于各图表 tooltip 展示）
          final unifiedDateText = _getDateText(touchedIndex, chartType);

          setState(() {
            // X坐标可以直接使用点击的坐标，因为三个图表的X轴是对齐的
            final centerX = localOffset.dx;
            final chartHeight = getChartHeight(chartContainerPadding);

            final barBottomY = chartHeight - bottomeservedSize;

            // 2. 为每个图表计算对应的Y坐标（柱状图顶部）
            final consumeBarTopY = calculateChartBarTopY(touchedIndex, data, barBottomY, chartHeight, adjustMaxY, bottomeservedSize);

            tooltipData = CustomTooltipData(
              centerX: centerX,
              barTopY: consumeBarTopY,
              valueText: '${data[touchedIndex].y?.toStringAsFixed(0) ?? '0'} min',
              dateText: unifiedDateText,
            );
            // 设置所有图表的 tooltip 和选中索引
            selectedIndex = touchedIndex;
          });

          // 调用 tooltip 显示回调
          final value = data[touchedIndex].y?.toDouble() ?? 0.0;
          widget.onTooltipShow?.call(touchedIndex, chartType, value);
        } else {
          // 点击了空白区域或值为0的柱状图，隐藏所有 tooltip
          setState(() {
            tooltipData = null;
            selectedIndex = null;
          });

          // 调用 tooltip 隐藏回调
          widget.onTooltipHide?.call();
        }
      } else {
        // 点击了空白区域，隐藏所有 tooltip
        setState(() {
          tooltipData = null;
          selectedIndex = null;
        });

        // 调用 tooltip 隐藏回调
        widget.onTooltipHide?.call();
      }
    }
  }

  // 添加关闭 tooltips 的方法
  void hideAllTooltips() {
    if (mounted) {
      setState(() {
        tooltipData = null;
        selectedIndex = null;
      });
      // 调用 tooltip 隐藏回调
      widget.onTooltipHide?.call();
    }
  }

  // 获取日期文本
  String _getDateText(int index, int chartType) {
    // 获取实际日期数据
    if (index < (dataCenterChartResp?.datas?.length ?? 0) && dataCenterChartResp?.datas?[index] != null) {
      final trainChartModel = dataCenterChartResp?.datas?[index];

      switch (chartType) {
        case 1:
          // 当 chartType 为 1 时，显示时间范围（几点几分到几点几分）
          if (trainChartModel?.startDate != null && trainChartModel?.endDate != null) {
            try {
              final startDate = DateTime.fromMillisecondsSinceEpoch(trainChartModel!.startDate!);
              final endDate = DateTime.fromMillisecondsSinceEpoch(trainChartModel.endDate!);

              // 格式化时间：HH:mm
              final startTime = '${startDate.hour.toString().padLeft(2, '0')}:${startDate.minute.toString().padLeft(2, '0')}';
              final endTime = '${endDate.hour.toString().padLeft(2, '0')}:${endDate.minute.toString().padLeft(2, '0')}';

              return '$startTime-$endTime';
            } catch (e) {
              return index.toString();
            }
          } else if (trainChartModel?.startDateString != null && trainChartModel?.endDateString != null) {
            // 如果有字符串格式的时间，直接使用
            return '${trainChartModel!.startDateString}-${trainChartModel.endDateString}';
          }
          break;
        case 2:
        case 3:
        case 4:
          // 其他类型显示月份和日期
          if (trainChartModel?.startDate != null) {
            try {
              final actualDate = DateTime.fromMillisecondsSinceEpoch(trainChartModel!.startDate!);
              final months = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
              return '${months[actualDate.month - 1]} ${actualDate.day} 号';
            } catch (e) {
              return index.toString();
            }
          }
          break;
      }
    }

    return index.toString();
  }
}
