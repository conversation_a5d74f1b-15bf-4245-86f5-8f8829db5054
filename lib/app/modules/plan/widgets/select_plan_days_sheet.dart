import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gap/gap.dart';


class SelectPlanDaysSheet extends StatelessWidget {
  final Function(int days) onDaysSelected;

  const SelectPlanDaysSheet({
    super.key,
    required this.onDaysSelected,
  });

  static void show(BuildContext context, {required Function(int days) onDaysSelected}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SelectPlanDaysSheet(onDaysSelected: onDaysSelected),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Safe<PERSON>rea(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '选择学习计划天数',
                    style: Get.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              Gap(8),
              Text(
                '请选择您想要的学习计划天数',
                style: Get.textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Gap(24),

              // 天数选择选项
              _buildDaysOption(context, 7, '7天', '适合初学者，快速体验'),
              Gap(12),
              _buildDaysOption(context, 14, '14天', '标准计划，循序渐进'),
              Gap(12),
              _buildDaysOption(context, 28, '28天', '完整计划，全面提升'),
              Gap(24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDaysOption(BuildContext context, int days, String title, String subtitle) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop();
        onDaysSelected(days);
      },
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // 天数图标
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Get.theme.primaryColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  '$days',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            Gap(16),
            // 文字信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Get.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Gap(4),
                  Text(
                    subtitle,
                    style: Get.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            // 箭头图标
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }
} 