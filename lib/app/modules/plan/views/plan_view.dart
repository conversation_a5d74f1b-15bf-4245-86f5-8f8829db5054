import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/utils/image.dart';

import 'package:lsenglish/utils/time.dart';

import 'package:lsenglish/model/learning_plan_resp/learning_plan_resp.dart';
import 'package:lsenglish/model/learning_plan_resp/plan_stage_resp.dart';
import 'package:lsenglish/model/learning_plan_resp/plan_week_resp.dart';
import 'package:lsenglish/model/learning_plan_resp/plan_day_resp.dart';
import 'package:lsenglish/model/learning_plan_resp/plan_resource_resp.dart';

import '../controllers/plan_controller.dart';

// 当天卡片组件 - 包含动画和完整内容
class TodayCard extends StatefulWidget {
  final PlanDayResp day;
  final PlanResourceResp? resource;
  final int currentDaySentences;
  final VoidCallback onStartLearning;

  const TodayCard({
    super.key,
    required this.day,
    this.resource,
    required this.currentDaySentences,
    required this.onStartLearning,
  });

  @override
  State<TodayCard> createState() => _TodayCardState();
}

class _TodayCardState extends State<TodayCard> with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _bounceController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _bounceAnimation;
  int _previousSentences = 0;
  OverlayEntry? _overlayEntry;
  late final GlobalKey _cardKey;
  RenderBox? _renderBox;
  Offset? _cardPosition;
  Size? _cardSize;

  @override
  void initState() {
    super.initState();
    _previousSentences = widget.currentDaySentences;
    _cardKey = GlobalKey(debugLabel: 'TodayCard_${widget.day.id}');

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.5,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.bounceOut,
    ));
  }

  @override
  void didUpdateWidget(TodayCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.currentDaySentences != _previousSentences && widget.currentDaySentences > _previousSentences) {
      _triggerAnimation();
    }
  }

  void _triggerAnimation() {
    _scrollToCardPosition().then((_) {
      _startAnimation();
    });
  }

  Future<void> _scrollToCardPosition() async {
    await Future.delayed(Duration.zero);
    final planController = Get.find<PlanController>();
    final scrollController = planController.scrollController;

    if (scrollController.hasClients && _cardKey.currentContext != null) {
      final renderBox = _cardKey.currentContext!.findRenderObject() as RenderBox;
      final cardPosition = renderBox.localToGlobal(Offset.zero);
      final cardSize = renderBox.size;
      final screenHeight = MediaQuery.of(context).size.height;
      final topPadding = MediaQuery.of(context).padding.top;
      final availableHeight = screenHeight - topPadding;
      final cardTop = cardPosition.dy;
      final cardBottom = cardTop + cardSize.height;
      final visibleTop = topPadding;
      final visibleBottom = screenHeight;
      final isFullyVisible = cardTop >= visibleTop && cardBottom <= visibleBottom;

      if (isFullyVisible) {
        return;
      }

      final targetOffset = scrollController.offset + cardPosition.dy - (availableHeight / 2) + (cardSize.height / 2);
      final clampedOffset = targetOffset.clamp(0.0, scrollController.position.maxScrollExtent);

      await scrollController.animateTo(
        clampedOffset,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  void _startAnimation() {
    _getCardPosition();
    _scaleController.reset();
    _bounceController.reset();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scaleController.forward().then((_) {
        _bounceController.forward();
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _previousSentences = widget.currentDaySentences;
            setState(() {});
            _overlayEntry?.markNeedsBuild();

            Future.delayed(const Duration(milliseconds: 300), () {
              if (mounted) {
                _scaleController.reverse().then((_) {
                  _removeOverlay();
                });
              }
            });
          }
        });
      });
    });
  }

  void _getCardPosition() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_cardKey.currentContext != null) {
        _renderBox = _cardKey.currentContext!.findRenderObject() as RenderBox;
        _cardPosition = _renderBox!.localToGlobal(Offset.zero);
        _cardSize = _renderBox!.size;
      }
    });
  }

  void _showOverlay() {
    if (_cardPosition == null || _cardSize == null) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: _cardPosition!.dx,
        top: _cardPosition!.dy,
        child: Material(
          color: Colors.transparent,
          child: Container(
            width: _cardSize!.width,
            height: _cardSize!.height,
            child: _buildOverlayContent(),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  void dispose() {
    _removeOverlay();
    _scaleController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimation, _bounceAnimation]),
      builder: (context, child) {
        if (_scaleAnimation.value > 1.0 && _overlayEntry == null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _showOverlay();
          });
        }

        double opacity = 1.0;
        if (_scaleAnimation.value > 1.05) {
          opacity = 0.0;
        }

        return Opacity(
          opacity: opacity,
          child: Container(
            key: _cardKey,
            child: _buildTodayCardContent(),
          ),
        );
      },
    );
  }

  Widget _buildTodayCardContent() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 时间轴圆点
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: Center(
              child: TimelineDot(
                isCompleted: widget.day.status == 2,
                isFinished: widget.day.status == 1,
                isCurrent: true,
                color: Colors.green,
                currentSentences: _previousSentences,
                targetSentences: widget.day.targetSentences,
              ),
            ),
          ),

          // 当天卡片内容
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: _buildTodayContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverlayContent() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: Center(
              child: TimelineDot(
                isCompleted: false,
                isCurrent: true,
                color: Colors.green,
                currentSentences: widget.currentDaySentences,
                targetSentences: widget.day.targetSentences,
              ),
            ),
          ),

          Expanded(
            child: AnimatedBuilder(
              animation: Listenable.merge([_scaleAnimation, _bounceAnimation]),
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  alignment: Alignment.centerLeft,
                  child: Transform.translate(
                    offset: Offset(
                      0,
                      -20 * (_scaleAnimation.value - 1.0) + 5 * _bounceAnimation.value,
                    ),
                    child: Transform.rotate(
                      angle: 0.02 * _bounceAnimation.value,
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1 + _bounceAnimation.value * 0.15),
                              blurRadius: 12 + _bounceAnimation.value * 8,
                              offset: Offset(0, 4 + _bounceAnimation.value * 2),
                            ),
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05 + _bounceAnimation.value * 0.1),
                              blurRadius: 6 + _bounceAnimation.value * 4,
                              offset: Offset(0, 2 + _bounceAnimation.value * 1),
                            ),
                          ],
                        ),
                        child: _buildTodayContent(),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTodayContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${widget.day.studyTimestamp != null ? DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(widget.day.studyTimestamp!)) : ''} - Today',
          style: Get.textTheme.bodyMedium,
        ),
        const Gap(4),
        Text(
          'Day${widget.day.planDayNumber}',
          style: Get.textTheme.headlineMedium,
        ),
        const Gap(12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _previousSentences.toString(),
                    style: TextStyle(
                        fontSize: 24,
                        color: _previousSentences > (widget.day.targetSentences ?? 0) ? Get.theme.primaryColor : Colors.black,
                        fontWeight: FontWeight.bold),
                  ),
                  Text(
                    "${widget.day.targetSentences?.toString() ?? "--"} sent.",
                    style: const TextStyle(fontSize: 12, color: Colors.grey, decoration: TextDecoration.underline),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    (widget.day.averageScore ?? 0).toStringAsFixed(0),
                    style: const TextStyle(fontSize: 24, color: Colors.black, fontWeight: FontWeight.bold),
                  ),
                  const Text(
                    "avg.score",
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    millisecondsToMinutesString(widget.day.totalLearnDuration ?? 0, precision: 0),
                    style: const TextStyle(fontSize: 24, color: Colors.black, fontWeight: FontWeight.bold),
                  ),
                  const Text(
                    "min",
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ),
          ],
        ),
        const Gap(8),
        AspectRatio(
          aspectRatio: 16 / 9,
          child: Container(
            clipBehavior: Clip.hardEdge,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: Colors.grey[200],
            ),
            child: AspectRatio(
              aspectRatio: 2,
              child: Stack(
                children: [
                  Positioned.fill(child: ImageLoader(widget.resource?.resourceCover ?? '')),
                  Center(child: ImageLoader(R.play, color: Colors.white)),
                  Positioned.fill(child: Container(color: Colors.black.withValues(alpha: 0.1))),
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        widget.resource?.resourceName ?? '',
                        style: Get.textTheme.titleMedium?.copyWith(color: Colors.white),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const Gap(10),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: widget.onStartLearning,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            child: const Text('开始学习', style: TextStyle(fontSize: 19)),
          ),
        ),
      ],
    );
  }
}

// 普通卡片组件 - 统一的UI结构
class DayCard extends StatelessWidget {
  final PlanDayResp day;
  final PlanResourceResp? resource;
  final bool isCurrentDay;
  final VoidCallback? onStartLearning;

  const DayCard({
    super.key,
    required this.day,
    this.resource,
    this.isCurrentDay = false,
    this.onStartLearning,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 时间轴圆点
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: Center(
              child: TimelineDot(
                isCompleted: day.status == 1 || day.status == 2,
                isFinished: day.status == 2,
                isCurrent: isCurrentDay,
                currentSentences: day.currentSentences,
                targetSentences: day.targetSentences,
              ),
            ),
          ),

          // 卡片内容
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: _buildCardContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardContent() {
    return Row(
      children: [
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                day.studyTimestamp != null ? DateFormat('MM,dd').format(DateTime.fromMillisecondsSinceEpoch(day.studyTimestamp!)) : '',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const Gap(4),
              Text(
                'Day${day.planDayNumber}',
                style: Get.textTheme.headlineMedium,
              ),
              const Gap(4),
              Text(
                "${day.currentSentences}/${day.targetSentences} sent.",
                style: Get.textTheme.bodyLarge,
              ),
              Text(
                resource?.resourceName ?? '',
                style: Get.textTheme.bodyLarge,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        const Gap(12),
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: ImageLoader(resource?.resourceCover ?? '', size: 86),
        ),
      ],
    );
  }
}

// 休息日卡片组件
class RestDayCard extends StatelessWidget {
  final PlanDayResp day;

  const RestDayCard({
    super.key,
    required this.day,
  });

  @override
  Widget build(BuildContext context) {
    final weekDayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    String weekDayName = '';
    if (day.studyTimestamp != null) {
      final dateTime = DateTime.fromMillisecondsSinceEpoch(day.studyTimestamp!);
      final weekday = dateTime.weekday;
      weekDayName = weekDayNames[weekday - 1];
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 休息日时间轴圆点
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: Center(
              child: TimelineDot(
                isRest: true,
                isCurrent: false,
                color: Get.theme.primaryColor,
              ),
            ),
          ),

          // 休息日卡片
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Get.theme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Get.theme.primaryColor,
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        weekDayName,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      const Gap(4),
                      Text(
                        '休息日',
                        style: Get.textTheme.headlineMedium?.copyWith(
                          color: Get.theme.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Gap(4),
                      Text(
                        '休息一下，恢复能量！',
                        style: Get.textTheme.bodyMedium?.copyWith(
                          color: Get.theme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  Icon(
                    Icons.local_drink_outlined,
                    size: 48,
                    color: Get.theme.primaryColor,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 完成指示器组件 - 显示在当天UI前面
class CompletionIndicator extends StatelessWidget {
  const CompletionIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 时间轴圆点位置（保持对齐）
        const Padding(
          padding: EdgeInsets.only(right: 10),
          child: SizedBox(
            width: 32,
            height: 32,
          ),
        ),

        // 完成指示器
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    height: 1,
                    color: Colors.grey[400],
                  ),
                ),
                const Gap(12),
                Text(
                  '已结束的学习',
                  style: Get.textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[400],
                  ),
                ),
                const Gap(12),
                Expanded(
                  child: Container(
                    height: 1,
                    color: Colors.grey[400],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// 时间轴圆点组件
class TimelineDot extends StatelessWidget {
  final bool isCompleted;
  final bool isFinished;
  final bool isRest;
  final bool isCurrent;
  final Color? color;
  final Widget? child;
  final int? currentSentences;
  final int? targetSentences;

  const TimelineDot({
    super.key,
    this.isCompleted = false,
    this.isFinished = false,
    this.isRest = false,
    this.isCurrent = false,
    this.color,
    this.child,
    this.currentSentences,
    this.targetSentences,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 32,
      height: 32,
      child: Center(
        child: _buildDot(),
      ),
    );
  }

  Widget _buildDot() {
    // 休息日：纯色圆
    if (isRest) {
      return Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: color ?? Colors.blue.shade300,
        ),
      );
    }

    // 完成过的：带打勾的圆
    if (isCompleted) {
      return Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: color ?? Colors.green,
        ),
        child: Visibility(
          visible: isFinished,
          child: const Center(
            child: Icon(
              Icons.check,
              size: 12,
              color: Colors.white,
            ),
          ),
        ),
      );
    }

    // 当天的：显示饼图进度
    if (isCurrent) {
      final progress = (currentSentences != null && targetSentences != null && targetSentences! > 0) ? currentSentences! / targetSentences! : 0.0;
      return SizedBox(
        width: 20,
        height: 20,
        child: CustomPaint(
          painter: PieChartPainter(
            data: [progress, 1.0 - progress],
            colors: [color ?? Colors.green, Colors.white],
          ),
        ),
      );
    }

    // 其他情况：默认样式（渐变圆环）
    return Container(
      width: 28,
      height: 28,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            (color ?? Colors.grey[400]!).withValues(alpha: 0.3),
            (color ?? Colors.grey[400]!).withValues(alpha: 0.1),
            Colors.transparent,
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
      ),
      child: Center(
        child: Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white,
            border: Border.all(
              color: color ?? Colors.grey[300]!,
              width: 2,
            ),
          ),
          child: child,
        ),
      ),
    );
  }
}

/// 饼图绘制器
class PieChartPainter extends CustomPainter {
  final List<double> data;
  final List<Color> colors;
  final double strokeWidth;

  PieChartPainter({
    required this.data,
    required this.colors,
    this.strokeWidth = 2.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width / 2) - strokeWidth;

    final total = data.fold(0.0, (sum, value) => sum + value);
    if (total <= 0) return;

    final borderPaint = Paint()
      ..color = Colors.grey[200]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    canvas.drawCircle(center, radius + strokeWidth / 2, borderPaint);

    double startAngle = -3.14159 / 2;

    for (int i = 0; i < data.length; i++) {
      if (data[i] <= 0) continue;

      final sweepAngle = (data[i] / total) * 2 * 3.14159;
      final color = i < colors.length ? colors[i] : Colors.grey;

      final paint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;

      final rect = Rect.fromCircle(center: center, radius: radius);
      canvas.drawArc(rect, startAngle, sweepAngle, true, paint);

      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is PieChartPainter) {
      return oldDelegate.data != data || oldDelegate.colors != colors || oldDelegate.strokeWidth != strokeWidth;
    }
    return true;
  }
}

class PlanView extends GetView<PlanController> {
  const PlanView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF6F6F6),
      body: Padding(
        padding: const EdgeInsets.only(top: 54),
        child: Column(
          children: [
            Expanded(
              child: Obx(() {
                final plan = controller.currentPlan.value;
                if (plan == null) {
                  return _buildEmptyState();
                }
                return _buildPlanContent(plan);
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.schedule,
            size: 64,
            color: Colors.grey[400],
          ),
          const Gap(16),
          Text(
            '暂无学习计划',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const Gap(8),
          Text(
            '点击下方按钮生成您的专属学习计划',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const Gap(24),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: controller.isLoading.value ? null : controller.showSelectPlanDaysSheet,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Get.theme.primaryColor,
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: controller.isLoading.value
                    ? const SizedBox(
                        height: 16,
                        width: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text('生成学习计划'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanContent(LearningPlanResp plan) {
    return SingleChildScrollView(
      controller: controller.autoScrollController,
      padding: const EdgeInsets.only(right: 16, left: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildPlanTitle(plan),
          const Gap(24),
          if (plan.stages != null) ...[
            ..._buildStagesWithRestDay(plan),
          ],
        ],
      ),
    );
  }

  List<Widget> _buildStagesWithRestDay(LearningPlanResp plan) {
    return plan.stages!.map((stage) => _buildStageSection(plan, stage)).toList();
  }

  Widget _buildPlanTitle(LearningPlanResp plan) {
    return GestureDetector(
      onTap: controller.showAdjustPlanSheet,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Text(
              '${plan.totalLearnDays}-Day 开口说英语',
              style: Get.textTheme.headlineMedium,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const Gap(16),
          Icon(
            Icons.edit,
            size: 20,
            color: Colors.grey[600],
          ),
        ],
      ),
    );
  }

  Widget _buildStageSection(LearningPlanResp plan, PlanStageResp stage) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...stage.weeks.map((week) => _buildWeekSection(plan, week)),
        const Gap(32),
      ],
    );
  }

  Widget _buildWeekSection(LearningPlanResp plan, PlanWeekResp week) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildWeekHeader(plan, week),
        const Gap(16),
        _buildDaysTimeline(week),
        const Gap(24),
      ],
    );
  }

  Widget _buildWeekHeader(LearningPlanResp plan, PlanWeekResp week) {
    String dateRange = _calculateWeekDateRange(week);
    int currentProgress = _calculateWeekProgress(plan, week);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'Week${week.weekNumber}',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const Gap(4),
        Text(
          dateRange,
          style: Get.textTheme.bodyMedium,
        ),
        const Gap(8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ...List.generate(
                week.days.length,
                (index) => Container(
                      margin: const EdgeInsets.only(right: 4),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: index < currentProgress ? Colors.green : Colors.grey[300],
                      ),
                    )),
            const Gap(8),
            Text(
              '$currentProgress/${week.days.length}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _calculateWeekDateRange(PlanWeekResp week) {
    if (week.days.isEmpty) {
      return 'No dates available';
    }

    final daysWithDate = week.days.where((day) => day.studyTimestamp != null && day.studyTimestamp! > 0).toList();

    if (daysWithDate.isEmpty) {
      return 'No dates available';
    }

    DateTime? earliestDate;
    DateTime? latestDate;

    for (final day in daysWithDate) {
      try {
        final date = DateTime.fromMillisecondsSinceEpoch(day.studyTimestamp!);
        if (earliestDate == null || date.isBefore(earliestDate)) {
          earliestDate = date;
        }
        if (latestDate == null || date.isAfter(latestDate)) {
          latestDate = date;
        }
      } catch (e) {
        continue;
      }
    }

    if (earliestDate == null || latestDate == null) {
      return 'Invalid date format';
    }

    String formatDate(DateTime date) {
      return DateFormat('MM,dd').format(date);
    }

    final startDate = formatDate(earliestDate);
    final endDate = formatDate(latestDate);

    if (earliestDate.isAtSameMomentAs(latestDate)) {
      return startDate;
    }

    return '$startDate - $endDate';
  }

  int _calculateWeekProgress(LearningPlanResp plan, PlanWeekResp week) {
    int completedDays = 0;

    for (final day in week.days) {
      if (day.status == 2 && day.type != 2) {
        completedDays++;
      }
    }

    return completedDays;
  }

  Widget _buildDaysTimeline(PlanWeekResp week) {
    final plan = controller.currentPlan.value;
    int currentDayIndex = -1;
    for (int i = 0; i < week.days.length; i++) {
      if (week.days[i].studyTimestamp == plan?.currentDayTimestamp) {
        currentDayIndex = i;
        break;
      }
    }

    // 判断当天之前是否有已完成的学习
    bool hasCompletedPreviousDays = false;
    if (currentDayIndex > 0) {
      for (int i = 0; i < currentDayIndex; i++) {
        if (week.days[i].status == 2 && week.days[i].type != 2) {
          hasCompletedPreviousDays = true;
          break;
        }
      }
    }

    return Stack(
      children: [
        // 时间轴虚线
        Positioned(
          left: 16,
          top: 0,
          bottom: 0,
          child: Container(
            width: 1,
            decoration: BoxDecoration(
              border: Border(
                left: BorderSide(
                  color: Colors.grey[300]!,
                  width: 1,
                  style: BorderStyle.solid,
                ),
              ),
            ),
          ),
        ),

        // 日计划卡片
        Column(
          children: week.days.asMap().entries.map((entry) {
            final index = entry.key;
            final day = entry.value;

            // 如果是当天UI，且当天之前有已完成的学习，在其前面显示完成指示器
            if (index == currentDayIndex && hasCompletedPreviousDays) {
              return Column(
                children: [
                  const CompletionIndicator(),
                  const Gap(16),
                  _buildDayCard(day),
                ],
              );
            }

            return _buildDayCard(day);
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDayCard(PlanDayResp day) {
    final plan = controller.currentPlan.value;
    final isCurrentDay = plan?.currentDayTimestamp == day.studyTimestamp;
    final isRestDay = day.type == 2;

    // 计算当前天在列表中的索引
    int dayIndex = _calculateDayIndex(day, plan);

    // 根据不同类型返回对应的卡片组件
    if (isRestDay) {
      return _buildRestDayCard(day, dayIndex);
    } else if (isCurrentDay) {
      return _buildTodayCard(day, dayIndex);
    } else {
      return _buildNormalDayCard(day, dayIndex);
    }
  }

  int _calculateDayIndex(PlanDayResp day, LearningPlanResp? plan) {
    int dayIndex = 0;
    bool found = false;
    for (final stage in plan?.stages ?? []) {
      for (final week in stage.weeks) {
        for (final weekDay in week.days) {
          if (weekDay.id == day.id) {
            found = true;
            break;
          }
          dayIndex++;
        }
        if (found) break;
      }
      if (found) break;
    }
    return dayIndex;
  }

  Widget _buildRestDayCard(PlanDayResp day, int dayIndex) {
    return AutoScrollTag(
      key: ValueKey('rest_day_card_${day.id}'),
      index: dayIndex,
      controller: controller.autoScrollController,
      child: RestDayCard(day: day),
    );
  }

  Widget _buildTodayCard(PlanDayResp day, int dayIndex) {
    return AutoScrollTag(
      key: ValueKey('today_card_${day.id}'),
      index: dayIndex,
      controller: controller.autoScrollController,
      child: TodayCard(
        day: day,
        resource: day.resources?.first,
        currentDaySentences: controller.currentDaySentences.value,
        onStartLearning: () => controller.startLearning(day),
      ),
    );
  }

  Widget _buildNormalDayCard(PlanDayResp day, int dayIndex) {
    return AutoScrollTag(
      key: ValueKey('day_card_${day.id}'),
      index: dayIndex,
      controller: controller.autoScrollController,
      child: DayCard(
        day: day,
        resource: day.resources?.first,
        isCurrentDay: false,
      ),
    );
  }
}
