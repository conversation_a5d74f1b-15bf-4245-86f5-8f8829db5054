import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/model/local_detail_resp/resource_detail_resp.dart';
import 'package:lsenglish/video/media_player.dart';
import 'package:lsenglish/video/video_controls_material.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

class DetailState {
  var isLocalVideo = false;
  var remoteResourceId = "";
  //进入页面的时候优先判断，免得每次在录制的判断，影响效率
  var videoKit = MediaPlayer();
  var isLandscape = false.obs;
  //是否为用户手动跳转
  var pageChangeByUser = false.obs;
  VideoController? videoController;
  var isVideoControllerInitialized = false.obs;
  final AutoScrollController itemScrollController = AutoScrollController(axis: Axis.vertical);
  late PageController pageController;
  var isPageControllerInitialized = false.obs;
  bool allowAutoScroll = true;
  Timer? autoScrollTimer;
  var videoUrlOrPath = "";
  var videoName = "".obs;
  ResourceDetailResp? localDetailResp;
  var currentPage = 0.obs;
  var loadingSubtitle = true.obs;
  var currentToast = "".obs;
  var showCurrentLandScapeToast = false.obs;
  late var videoControlsCallbacks = VideoControlsCallbacks(
    onToggleFullscreen: () => videoKit.toggleFullscreen(),
    onDoubleTapPlayOrPause: () => videoKit.resetLsModeIndex(needPlay: false),
    onPlayOrPause: () => videoKit.resetLsModeIndex(),
    onPointerUp: () => videoKit.resetLsModeIndex(needPlay: false),
    onVideoSeekChangeEnd: () => videoKit.onVideoSeekChangeEnd(),
    onTap: () => videoKit.toggleFullscreen(),
  );
  var totalDuration = 0.obs;
  var seekBarRatio = 0.0.obs;
  var canChangeSeekBarRatio = true;
  var isAppBarVisible = true.obs;
  var dayId = ""; // 添加dayId变量

  /// 初始化状态
  void initialize() {
    // 初始化 PageController
    pageController = PageController(keepPage: false);
    isPageControllerInitialized.value = true;

    // 初始化 videoKit
    videoKit.init();
    videoKit.reset();

    // 初始化 videoController
    videoController = VideoController(videoKit.getPlayer);
    isVideoControllerInitialized.value = true;

    // 从 Get.arguments 获取参数
    remoteResourceId = Get.arguments['resourceId'] ?? "";
    videoUrlOrPath = Get.arguments['videoUrlOrPath'] ?? "";
    videoName.value = Get.arguments['videoName'] ?? "";
    dayId = Get.arguments['dayId'] ?? ""; // 获取dayId参数

    // 判断是否为本地视频
    isLocalVideo = remoteResourceId.isEmpty;
  }
}
