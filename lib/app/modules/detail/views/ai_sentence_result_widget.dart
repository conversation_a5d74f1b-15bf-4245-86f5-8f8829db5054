import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/log.dart';

import 'package:lsenglish/utils/subtitle/src/core/models.dart';
import 'package:lsenglish/utils/toast.dart';
import 'package:lsenglish/widgets/base_dialog.dart';

//string: 分析结果 int:  1为覆盖 2为添加至笔记后
typedef AiSentenceSaveCallback = void Function(String, int);

class AiSentenceResultWidget extends StatefulWidget {
  final Subtitle subtitle;
  final bool isInputEmpty;
  final AiSentenceSaveCallback aiSentenceSaveCallback;

  const AiSentenceResultWidget({
    super.key,
    required this.subtitle,
    required this.aiSentenceSaveCallback,
    this.isInputEmpty = false,
  });

  @override
  State<AiSentenceResultWidget> createState() => _AiSentenceResultWidgetState();
}

class _AiSentenceResultWidgetState extends State<AiSentenceResultWidget> {
  var content = "";
  var englishSource = "";
  bool needCancelToken = false;
  final ScrollController _scrollController = ScrollController();

  void fetchAI() async {
    SmartDialog.showLoading();
    try {
      needCancelToken = false;
      final cancelToken = CancelToken();
      final response = await Net.getDio().post(
        'https://dashscope.aliyuncs.com/api/v1/apps/a12e3048c572477194214b30a24b980f/completion',
        options: Options(
          headers: {
            'Authorization': 'Bearer sk-3028b3c320aa4870b9d35e6885d4b2ef',
            'Content-Type': 'application/json',
            'X-DashScope-SSE': 'enable',
          },
          responseType: ResponseType.stream,
        ),
        data: jsonEncode({
          'input': {
            'prompt': englishSource,
          },
          'parameters': {
            'incremental_output': 'true',
          },
        }),
        cancelToken: cancelToken,
      );

      // Use transform to decode the stream properly
      response.data.stream.cast<List<int>>().transform(utf8.decoder).transform(const LineSplitter()).listen((data) {
        final jsonStartIndex = data.indexOf('{');
        if (jsonStartIndex != -1) {
          final jsonString = data.substring(jsonStartIndex);
          try {
            final jsonData = jsonDecode(jsonString);
            SmartDialog.dismiss();
            setState(() {
              content = content + jsonData['output']['text'];
            });
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
            });
          } catch (e) {
            logger('JSON decode error: $e');
          }
        }
        // Example condition to cancel
        if (needCancelToken) {
          cancelToken.cancel();
          needCancelToken = false;
        }
      }, onDone: () {
        logger('Stream finished.');
        SmartDialog.dismiss();
      }, onError: (error) {
        logger('Error: $error');
        SmartDialog.dismiss();
      });
    } catch (e) {
      logger('Error: $e');
      SmartDialog.dismiss();
    }
  }

  void save() {
    if (content.isEmpty) {
      "请等待分析结果...".toast;
      return;
    }
    if (widget.isInputEmpty) {
      widget.aiSentenceSaveCallback(content, 1);
    } else {
      Get.dialog(CommonDialog(
        title: '是否覆盖原有笔记?',
        subtitle: 'AI结果覆盖原有记录的笔记',
        options: const ["覆盖", "不覆盖，添加至笔记后"],
        callbacks: [
          () => {
                widget.aiSentenceSaveCallback(content, 1),
                Get.back(),
              },
          () => {
                widget.aiSentenceSaveCallback(content, 2),
                Get.back(),
              },
        ],
      ));
    }
  }

  @override
  void initState() {
    super.initState();
    englishSource = widget.subtitle.targetData;
    fetchAI();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 70),
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        padding: EdgeInsets.only(bottom: 34),
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: 12, horizontal: 14),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: const Icon(Icons.close),
                  ),
                  const Spacer(),
                  Text(
                    "AI解析结果",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xff555454),
                    ),
                  ),
                  const Spacer(),
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: SelectableText(
                      content,
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            GestureDetector(
              onTap: () => save(),
              child: Row(
                children: [
                  const Spacer(),
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Container(
                      width: 72,
                      height: 45,
                      decoration: BoxDecoration(
                        color: Get.theme.colorScheme.onSurface,
                        borderRadius: const BorderRadius.all(Radius.circular(12)),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      child: Center(
                        child: Text(
                          "保存",
                          style: TextStyle(color: Colors.white, fontSize: 16),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
