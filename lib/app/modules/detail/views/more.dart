import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/modules/detail/controllers/detail_controller.dart';
import 'package:lsenglish/app/modules/setting/views/subtitle_widget.dart';

import 'package:lsenglish/config/config.dart';

import '../../setting/views/player_widget.dart';
import '../../setting/views/setting_warp.dart';

class PlayerMoreWidget extends GetWidget<DetailController> {
  const PlayerMoreWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 56),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        child: Container(
          height: MediaQuery.of(context).size.height * 0.9,
          color: Get.theme.scaffoldBackgroundColor,
          child: Column(
            children: [
              AppBar(
                title: Text(
                  "更多",
                  style: Get.textTheme.titleLarge,
                ),
                backgroundColor: Get.theme.scaffoldBackgroundColor,
                leading: GestureDetector(onTap: () => Get.back(), child: const Icon(Icons.close_rounded)),
              ),
              Gap(20),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      GestureDetector(
                        onTap: () {
                          Get.back();
                          // controller.goSubtitleSearch();
                        },
                        child: SettingItem(
                          child: Row(
                            children: [
                              Text(
                                "导入字幕",
                                style: Get.textTheme.titleSmall,
                              ),
                            ],
                          ),
                        ),
                      ),
                      Gap(20),
                      SettingItem(
                        child: Row(
                          children: [
                            Text(
                              "反馈",
                              style: Get.textTheme.titleSmall,
                            ),
                            const Spacer(),
                            Icon(Icons.arrow_forward_ios_rounded, size: 16)
                          ],
                        ),
                      ),
                      Gap(20),
                      const PlayerSettingWidget(),
                      Gap(20),
                      const BackgroundPlaySettingWidget(),
                      Gap(20),
                      const SubtitleSettingWidget(),
                      Gap(20),
                      SettingItem(
                        child: Row(
                          children: [
                            Text(
                              "功能指引",
                              style: Get.textTheme.titleSmall,
                            ),
                            const Spacer(),
                            Icon(
                              Icons.arrow_forward_ios_rounded,
                              size: 16,
                            )
                          ],
                        ),
                      ),
                      Gap(30),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class BackgroundPlaySettingWidget extends StatelessWidget {
  const BackgroundPlaySettingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              "后台播放设置",
              style: Get.textTheme.bodyLarge
                  ?.copyWith(color: Get.theme.primaryColor),
            ),
          ),
          Gap(12),
          SettingItem(
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      "LS模式下每句播放遍数",
                      style: Get.textTheme.titleSmall,
                    ),
                    const Spacer(),
                    Obx(() => _buildRepeatCountDropdown()),
                  ],
                ),
                const Divider(),
                Row(
                  children: [
                    Text(
                      "LS模式下下一句间隔时间",
                      style: Get.textTheme.titleSmall,
                    ),
                    const Spacer(),
                    Obx(() => _buildDelayDropdown()),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRepeatCountDropdown() {
    final options = [
      {'value': 1, 'label': '1次'},
      {'value': 2, 'label': '2次'},
      {'value': 3, 'label': '3次'},
      {'value': 4, 'label': '4次'},
      {'value': 5, 'label': '5次'},
      {'value': 0, 'label': '无限次'},
    ];

    return DropdownButton<int>(
      value: Config().lsModeRepeatCount.value,
      underline: Container(),
      items: options.map((option) {
        return DropdownMenuItem<int>(
          value: option['value'] as int,
          child: Text(
            option['label'] as String,
            style: Get.textTheme.bodyMedium,
          ),
        );
      }).toList(),
      onChanged: (int? value) {
        if (value != null) {
          Config().setLsModeRepeatCount(value);
        }
      },
    );
  }

  Widget _buildDelayDropdown() {
    final options = [
      {'value': 0, 'label': '0秒'},
      {'value': 2, 'label': '2秒'},
      {'value': 4, 'label': '4秒'},
      {'value': 8, 'label': '8秒'},
      {'value': 15, 'label': '15秒'},
    ];

    return DropdownButton<int>(
      value: Config().lsModeNextSentenceDelay.value,
      underline: Container(),
      items: options.map((option) {
        return DropdownMenuItem<int>(
          value: option['value'] as int,
          child: Text(
            option['label'] as String,
            style: Get.textTheme.bodyMedium,
          ),
        );
      }).toList(),
      onChanged: (int? value) {
        if (value != null) {
          Config().setLsModeNextSentenceDelay(value);
        }
      },
    );
  }
}
