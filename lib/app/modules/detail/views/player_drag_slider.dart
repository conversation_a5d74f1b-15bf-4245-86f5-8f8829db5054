import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:lsenglish/utils/time.dart';

typedef DualSliderDragEndCallback = void Function(double ratio);

class DualSlider extends StatefulWidget {
  final DualSliderDragEndCallback onDragEnd;
  final double ratio;
  final int totalDuration;
  const DualSlider({
    super.key,
    required this.ratio,
    required this.onDragEnd,
    required this.totalDuration,
  });

  @override
  State<DualSlider> createState() => _DualSliderState();
}

class _DualSliderState extends State<DualSlider> {
  double _value = 0.3;
  bool _isDragging = false;
  int _totalDuration = 0;
  @override
  void initState() {
    super.initState();
    _value = widget.ratio;
  }

  @override
  void didUpdateWidget(covariant DualSlider oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.ratio != widget.ratio && !_isDragging || oldWidget.totalDuration != widget.totalDuration) {
      setState(() {
        _value = widget.ratio;
        _totalDuration = widget.totalDuration;
      });
    }
  }

  void _startDragging() {
    setState(() {
      _isDragging = true;
    });
  }

  void _endDragging() {
    setState(() {
      _isDragging = false;
    });
    widget.onDragEnd(_value);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: _isDragging ? 50.0 : 16.0,
      child: Stack(
        alignment: AlignmentDirectional.center,
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: EdgeInsets.symmetric(horizontal: _isDragging ? 70.0 : 0.0),
            child: SliderTheme(
              data: SliderTheme.of(context).copyWith(
                trackHeight: _isDragging ? 1 : 0.5,
                thumbShape: RoundSliderThumbShape(enabledThumbRadius: _isDragging ? 8 : 0, elevation: 0, pressedElevation: 0),
                overlayShape: SliderComponentShape.noOverlay,
                activeTrackColor: Get.theme.primaryColor,
                inactiveTrackColor: _isDragging ? Colors.grey : Colors.transparent,
                overlayColor: Colors.transparent,
              ),
              child: Slider(
                value: _value,
                min: 0.0,
                max: 1.0,
                onChanged: (newValue) {
                  setState(() {
                    _value = newValue;
                  });
                },
                onChangeStart: (_) => _startDragging(),
                onChangeEnd: (_) => _endDragging(),
              ),
            ),
          ),
          AnimatedPositioned(
            duration: const Duration(milliseconds: 300),
            left: _isDragging ? 20 : -50,
            top: 0,
            bottom: 0,
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 300),
              opacity: _isDragging ? 1.0 : 0.0,
              child: Center(
                child: Text(formatMilliseconds((_totalDuration * _value).toInt()), style: const TextStyle(fontSize: 16)),
              ),
            ),
          ),
          AnimatedPositioned(
            duration: const Duration(milliseconds: 300),
            right: _isDragging ? 20 : -50,
            top: 0,
            bottom: 0,
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 300),
              opacity: _isDragging ? 1.0 : 0.0,
              child: Center(
                child: Text(formatMilliseconds(_totalDuration), style: const TextStyle(fontSize: 16)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
