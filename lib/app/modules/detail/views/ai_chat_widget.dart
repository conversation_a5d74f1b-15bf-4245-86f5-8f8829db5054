import 'dart:convert';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/toast.dart';
import 'package:lsenglish/utils/volcengine_speech_recognition.dart';
import 'package:get/get.dart';
import 'package:lsenglish/widgets/audio_visualizer.dart';
import 'package:permission_handler/permission_handler.dart';

import 'package:lsenglish/utils/subtitle/src/core/models.dart';
import 'package:flutter_chat_core/flutter_chat_core.dart';
import 'markdown_message_widget.dart';

//string: 分析结果 int:  1为覆盖 2为添加至笔记后
typedef AiSentenceSaveCallback = void Function(String, int);

class AiChatWidget extends StatefulWidget {
  final Subtitle subtitle;
  final bool isInputEmpty;

  const AiChatWidget({
    super.key,
    required this.subtitle,
    this.isInputEmpty = false,
  });

  @override
  State<AiChatWidget> createState() => _AiChatWidgetState();
}

class _AiChatWidgetState extends State<AiChatWidget> {
  var content = "";
  var englishSource = "";
  bool needCancelToken = false;

  final _systemAuthorId = "system";
  final _userAuthorId = "user";
  var sessionId = "";
  var prompt = "";
  var modelMessages = <ModelMessage>[];
  final _chatController = InMemoryChatController();

  // 跟踪当前正在回复的消息 ID
  String? _currentAiResponseMessageId;

  // 跟踪用户是否主动滚动聊天列表
  bool _userHasScrolled = false;

  // 语音输入相关状态
  final VolcengineSpeechRecognition _speechRecognition = VolcengineSpeechRecognition();
  final RxBool _isVoiceRecording = false.obs;
  final RxBool _isVoiceInitialized = false.obs;
  final RxBool _hasMicrophonePermission = false.obs;
  final RxBool _isVoiceMode = false.obs; // 默认使用语音模式
  final RxString _voiceResult = ''.obs;
  final RxString _voiceError = ''.obs;

  // 录音手势状态
  final RxBool _isFingerOutsideArea = false.obs; // 手指是否在录音区域外
  final RxBool _shouldCancelRecording = false.obs; // 是否应该取消录音

  // 文字输入控制器
  final TextEditingController _textController = TextEditingController();
  final FocusNode _textFocusNode = FocusNode();
  final RxBool _isTextInputActive = false.obs;
  final RxString _inputText = ''.obs; // 用于跟踪输入文本状态

  // 软键盘状态监听
  final RxBool _isKeyboardVisible = false.obs;

  /// 获取限制数量的消息列表，最多返回10条最新的消息
  List<ModelMessage> _getLimitedMessages() {
    // 如果消息数量不超过10条，直接返回
    if (modelMessages.length <= 10) {
      return modelMessages;
    }

    // 保留系统消息（第一条）和最新的9条消息
    final limitedMessages = <ModelMessage>[];

    // 始终保留系统消息
    if (modelMessages.isNotEmpty) {
      limitedMessages.add(modelMessages.first);
    }

    // 添加最新的9条消息（不包括系统消息）
    final startIndex = modelMessages.length - 9;
    for (int i = startIndex; i < modelMessages.length; i++) {
      limitedMessages.add(modelMessages[i]);
    }

    return limitedMessages;
  }

  void fetchAI() async {
    setState(() {
      if (_chatController.messages.isEmpty) {
        final messageId = randomString();
        _chatController.insertMessage(
          Message.text(
            id: messageId,
            text: "正在解析句子${widget.subtitle.targetData},请稍后...",
            authorId: _systemAuthorId,
          ),
        );
        _currentAiResponseMessageId = messageId;
      } else {
        final messageId = randomString();
        _chatController.insertMessage(
          Message.text(
            id: messageId,
            text: "正在努力回答中...",
            authorId: _systemAuthorId,
          ),
        );
        _currentAiResponseMessageId = messageId;
      }
    });
    content = "";
    try {
      needCancelToken = false;
      final cancelToken = CancelToken();
      // https://www.volcengine.com/docs/82379/1298454#%E8%AF%B7%E6%B1%82%E4%BD%93
      final response = await Net.getDio().post(
        'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
        options: Options(
          headers: {
            'Authorization': 'Bearer 73490fa9-1d7b-42b1-80a4-4de4b21c82e2',
            'Content-Type': 'application/json',
          },
          responseType: ResponseType.stream,
        ),
        data: jsonEncode({
          'model': 'ep-20241124203623-4zmmh',
          'stream': true,
          "messages": _getLimitedMessages(),
        }),
        cancelToken: cancelToken,
      );

      // Use transform to decode the stream properly
      response.data.stream.cast<List<int>>().transform(utf8.decoder).transform(const LineSplitter()).listen((data) {
        // debugPrint("data=$data");

        // 检查是否是结束标记
        if (data == 'data: [DONE]') {
          debugPrint("DONEDONEDONEDONE content=$content");
          modelMessages.add(ModelMessage(role: 'assistant', content: content));
          // 清除当前回复状态，这样操作按钮就会显示出来
          _currentAiResponseMessageId = null;
          content = "";
          return;
        }

        final jsonStartIndex = data.indexOf('{');
        if (jsonStartIndex != -1) {
          final jsonString = data.substring(jsonStartIndex);
          try {
            final jsonData = jsonDecode(jsonString);
            setState(() {
              var result = jsonData['choices'][0]['delta']['content'];
              content = content + result;
              _chatController.updateMessage(
                  _chatController.messages.last,
                  Message.text(
                    id: _chatController.messages.last.id,
                    text: content,
                    authorId: _chatController.messages.last.authorId,
                  ));
              // 只有当用户没有主动滚动时才自动滚动到底部
              if (!_userHasScrolled) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _chatController.scrollToMessage(_chatController.messages.last.id);
                });
              }
            });
          } catch (e) {
            logger('JSON decode error: $e');
          }
        }

        // Example condition to cancel
        if (needCancelToken) {
          cancelToken.cancel();
          needCancelToken = false;
        }
      }, onDone: () {
        logger('Stream finished.');
        SmartDialog.dismiss();
      }, onError: (error) {
        logger('Error: $error');
        SmartDialog.dismiss();
      });
    } catch (e) {
      logger('Error: $e');
      SmartDialog.dismiss();
    }
  }

  @override
  void initState() {
    super.initState();
    englishSource = widget.subtitle.targetData;
    prompt = englishSource;
    modelMessages
        .add(ModelMessage(role: 'system', content: "你是一个英语老师，简单地解析下句子的语法和难点。并且根据我提供的大量句子来结合解析我后续给你发送的单独的句子的含义,不要使用markdown的格式，最大的回复量不要超过1000个字符"));
    modelMessages.add(ModelMessage(role: 'user', content: prompt));
    fetchAI();

    // 初始化语音识别
    _initVoiceRecognition();

    // 添加文本控制器监听器，用于触发UI更新
    _textController.addListener(() {
      _inputText.value = _textController.text;
    });
  }

  /// 监听软键盘状态变化
  void _onKeyboardVisibilityChanged(bool isVisible) {
    debugPrint("键盘状态变化: $isVisible");
    _isKeyboardVisible.value = isVisible;

    // 当软键盘隐藏时，设置文字输入为非活跃状态
    if (!isVisible) {
      _isTextInputActive.value = false;
    }
  }

  /// 初始化语音识别
  Future<void> _initVoiceRecognition() async {
    try {
      final success = await _speechRecognition.initialize();
      if (success) {
        _isVoiceInitialized.value = true;

        // 检查初始权限状态（不主动请求权限）
        final status = await Permission.microphone.status;
        _hasMicrophonePermission.value = status.isGranted;

        // 监听语音识别结果
        ever(_speechRecognition.finalResultObs, (result) {
          if (result.isNotEmpty) {
            debugPrint("AAAA 语音识别结果: $result");
          }
        });

        // 监听错误信息
        ever(_speechRecognition.errorMessageObs, (error) {
          if (error.isNotEmpty) {
            _voiceError.value = error;
            SmartDialog.showToast(error);
          }
        });
      }
    } catch (e) {
      logger('语音识别初始化失败: $e');
    }
  }

  /// 开始语音输入
  Future<void> _startVoiceInput() async {
    if (!_isVoiceInitialized.value) {
      '语音识别未初始化'.toast;
      return;
    }

    // 检查权限状态，如果没有权限就请求权限
    if (!_hasMicrophonePermission.value) {
      final result = await Permission.microphone.request();
      if (result.isGranted) {
        _hasMicrophonePermission.value = true;
      } else {
        SmartDialog.showToast('需要录音权限才能使用语音输入功能');
      }
      return;
    }

    try {
      _isVoiceRecording.value = true;
      _isFingerOutsideArea.value = false;
      _shouldCancelRecording.value = false;
      _voiceResult.value = '';
      _voiceError.value = '';

      // 添加震动反馈 - 开始录音
      HapticFeedback.lightImpact();

      final success = await _speechRecognition.startRealTimeRecording();
      // final success = true;
      if (!success) {
        _isVoiceRecording.value = false;
        SmartDialog.showToast('开始语音输入失败');
      }
    } catch (e) {
      _isVoiceRecording.value = false;
      SmartDialog.showToast('语音输入出错: $e');
    }
  }

  /// 停止语音输入
  Future<void> _stopVoiceInput() async {
    debugPrint("停止语音输入");
    setState(() {
      _isVoiceRecording.value = false;
    });
    try {
      await _speechRecognition.stopStreamingRecognition();

      // 根据手指位置决定是发送还是取消录音
      if (_shouldCancelRecording.value) {
        toastInfo('录音已取消');
        _speechRecognition.finalResultObs.value = '';
        // 添加震动反馈 - 取消录音
        HapticFeedback.mediumImpact();
      } else {
        // 发送录音
        final finalResult = _speechRecognition.getResult();
        debugPrint("AAAAA finalResult=$finalResult");
        if (finalResult.isNotEmpty) {
          _handleSendPressed(finalResult);
          // 清空结果，避免重复发送
          _speechRecognition.finalResultObs.value = '';
        }
      }
      setState(() {
        // 重置状态
        _isFingerOutsideArea.value = false;
        _shouldCancelRecording.value = false;
      });
    } catch (e) {
      logger('停止语音输入失败: $e');
      _isFingerOutsideArea.value = false;
      _shouldCancelRecording.value = false;
    }
  }

  /// 处理手指移动时的震动反馈
  void _handleFingerMoveFeedback(bool isOutsideArea) {
    if (_isVoiceRecording.value) {
      if (isOutsideArea && !_isFingerOutsideArea.value) {
        // 手指离开录音区域 - 添加震动反馈
        HapticFeedback.lightImpact();
      } else if (!isOutsideArea && _isFingerOutsideArea.value) {
        // 手指回到录音区域 - 添加震动反馈
        HapticFeedback.lightImpact();
      }
    }
  }

  /// 统一处理长按开始语音输入
  Future<void> _handleLongPressStart() async {
    await _startVoiceInput();
  }

  /// 统一处理长按结束语音输入
  void _handleLongPressEnd() {
    _stopVoiceInput();
  }

  /// 统一处理长按移动更新
  void _handleLongPressMoveUpdate(LongPressMoveUpdateDetails details, String modeName) {
    // 跟踪手指移动，判断是否离开录音区域
    if (_isVoiceRecording.value) {
      // 使用更敏感的阈值
      final isOutsideArea = details.offsetFromOrigin.dy < -15;

      // 处理震动反馈
      _handleFingerMoveFeedback(isOutsideArea);

      if (isOutsideArea) {
        if (!_isFingerOutsideArea.value) {
          _isFingerOutsideArea.value = true;
          _shouldCancelRecording.value = true;
          debugPrint('$modeName：手指离开录音区域，offset: ${details.offsetFromOrigin.dy}');
        }
      } else {
        if (_isFingerOutsideArea.value) {
          _isFingerOutsideArea.value = false;
          _shouldCancelRecording.value = false;
          debugPrint('$modeName：手指回到录音区域，offset: ${details.offsetFromOrigin.dy}');
        }
      }
    }
  }

  /// 构建支持点击和快速长按的手势检测器
  Widget _buildFastLongPressGestureDetectorWithTap({
    required Widget child,
    required String modeName,
    required VoidCallback onTap,
  }) {
    return Obx(() {
      // 当处于文字输入模式且输入框激活时，禁用长按手势
      final shouldDisableLongPress = !_isVoiceMode.value && _isTextInputActive.value;

      if (shouldDisableLongPress) {
        // 只保留点击手势
        return GestureDetector(
          onTap: onTap,
          child: child,
        );
      }

      // 正常模式：支持点击和长按
      return RawGestureDetector(
        gestures: {
          TapGestureRecognizer: GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(
            () => TapGestureRecognizer(),
            (TapGestureRecognizer instance) {
              instance.onTap = onTap;
            },
          ),
          LongPressGestureRecognizer: GestureRecognizerFactoryWithHandlers<LongPressGestureRecognizer>(
            () => LongPressGestureRecognizer(duration: const Duration(milliseconds: 200)), // 缩短到200毫秒
            (LongPressGestureRecognizer instance) {
              instance.onLongPressStart = (LongPressStartDetails details) {
                _handleLongPressStart();
              };
              instance.onLongPressEnd = (LongPressEndDetails details) {
                _handleLongPressEnd();
              };
              instance.onLongPressMoveUpdate = (LongPressMoveUpdateDetails details) {
                _handleLongPressMoveUpdate(details, modeName);
              };
            },
          ),
        },
        child: child,
      );
    });
  }

  String randomString() {
    final random = Random.secure();
    final values = List<int>.generate(16, (i) => random.nextInt(255));
    return base64UrlEncode(values);
  }

  @override
  void dispose() {
    // 清理语音识别资源
    _speechRecognition.dispose();
    // 清理文字输入控制器
    _textController.removeListener(() {
      _inputText.value = _textController.text;
    });
    _textController.dispose();
    _textFocusNode.dispose();
    super.dispose();
  }

  void _handleSendPressed(String message) {
    final textMessage = Message.text(
      id: randomString(),
      text: message,
      authorId: _userAuthorId,
    );

    prompt = message;
    setState(() {
      _chatController.insertMessage(textMessage);
    });
    modelMessages.add(ModelMessage(role: 'user', content: message));
    // 发送消息时重置用户滚动状态，并自动滚动到底部
    _userHasScrolled = false;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chatController.scrollToMessage(textMessage.id);
    });
    fetchAI();
  }

  @override
  Widget build(BuildContext context) {
    // 监听软键盘状态
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = bottomInset > 0;

    // 使用 WidgetsBinding.instance.addPostFrameCallback 来避免在 build 中直接调用 setState
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_isKeyboardVisible.value != isKeyboardVisible) {
        _onKeyboardVisibilityChanged(isKeyboardVisible);
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text("AI解析"),
      ),
      body: Column(
        children: [
          // 聊天消息区域
          Expanded(
            child: NotificationListener<ScrollNotification>(
              onNotification: (ScrollNotification notification) {
                // 检测用户是否主动滚动
                if (notification is ScrollUpdateNotification) {
                  // 如果用户向上滚动（查看历史消息），标记为用户主动滚动
                  if (notification.metrics.pixels < notification.metrics.maxScrollExtent - 50) {
                    _userHasScrolled = true;
                  }
                  // 如果用户滚动到底部，重置标记
                  if (notification.metrics.pixels >= notification.metrics.maxScrollExtent - 10) {
                    _userHasScrolled = false;
                  }
                }
                return false;
              },
              child: Chat(
                currentUserId: _userAuthorId,
                resolveUser: (String id) async {
                  return User(id: id);
                },
                theme: ChatTheme.light(),
                chatController: _chatController,
                builders: Builders(
                  textMessageBuilder: (
                    context,
                    message,
                    index, {
                    required bool isSentByMe,
                    MessageGroupStatus? groupStatus,
                  }) {
                    // 如果是当前正在回复的消息，则不显示操作按钮
                    final isAiCompleteResponse = message.id != _currentAiResponseMessageId;

                    return MarkdownMessageWidget(
                      message: message,
                      index: index,
                      isSentByMe: isSentByMe,
                      groupStatus: groupStatus,
                      isAiCompleteResponse: isAiCompleteResponse,
                    );
                  },
                  // 隐藏原来的输入框
                  composerBuilder: (context) => const SizedBox.shrink(),
                ),
              ),
            ),
          ),
          // 自定义输入区域
          _buildCustomInputArea(),
        ],
      ),
    );
  }

  /// 构建自定义输入区域
  Widget _buildCustomInputArea() {
    return _buildFastLongPressGestureDetectorWithTap(
      modeName: '语音模式',
      child: Column(
        children: [
          // 语音输入状态提示
          Obx(() {
            if (_isVoiceRecording.value) {
              return SizedBox(
                height: 20,
                child: Text(
                  _isFingerOutsideArea.value ? '松手取消' : '松手发送，上移取消',
                  style: TextStyle(color: _isFingerOutsideArea.value ? Colors.red : Colors.grey, fontSize: 12.0),
                ),
              );
            }
            return const SizedBox(height: 20);
          }),
          Padding(
            padding: const EdgeInsets.only(left: 12.0, right: 12.0, top: 10.0, bottom: 30.0),
            child: Obx(() {
              return Stack(
                children: [
                  Container(
                    height: 60,
                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                    decoration: BoxDecoration(
                      color: _isVoiceRecording.value ? (_isFingerOutsideArea.value ? Colors.red.shade500 : Colors.blue.shade500) : Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.shade300,
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Obx(() {
                          if (_isVoiceMode.value) {
                            // 语音输入模式
                            return Expanded(
                              child: Visibility(
                                visible: !_isVoiceRecording.value,
                                child: Text(
                                  '按住说话',
                                  style: Get.textTheme.titleMedium?.copyWith(color: Colors.black),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            );
                          } else {
                            // 文字输入模式
                            return Expanded(
                              child: Obx(() {
                                if (_isVoiceRecording.value) {
                                  return const SizedBox.shrink();
                                }
                                if (_isTextInputActive.value) {
                                  // 显示输入框
                                  return TextField(
                                    controller: _textController,
                                    focusNode: _textFocusNode,
                                    decoration: const InputDecoration(
                                      hintText: '发消息...',
                                      border: InputBorder.none,
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: 16.0,
                                        vertical: 15.0,
                                      ),
                                    ),
                                    style: Get.textTheme.titleMedium?.copyWith(color: Colors.grey, fontWeight: FontWeight.w400),
                                    maxLines: 1,
                                    textInputAction: TextInputAction.send,
                                    onSubmitted: (text) {
                                      if (text.trim().isNotEmpty) {
                                        _handleSendPressed(text.trim());
                                        _textController.clear();
                                      }
                                      _isTextInputActive.value = false;
                                      _textFocusNode.unfocus();
                                    },
                                    onTapOutside: (event) {
                                      _isTextInputActive.value = false;
                                      _textFocusNode.unfocus();
                                    },
                                  );
                                } else {
                                  // 显示提示文本或已输入的文本
                                  return Container(
                                    width: double.infinity,
                                    height: double.infinity,
                                    color: Colors.transparent,
                                    alignment: Alignment.centerLeft,
                                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                                    child: Obx(() {
                                      final hasText = _inputText.value.isNotEmpty;
                                      return Text(
                                        hasText ? _inputText.value : '发消息或按住说话...',
                                        style: TextStyle(
                                          color: hasText ? Colors.black : Colors.grey.shade600,
                                          fontSize: 16.0,
                                        ),
                                      );
                                    }),
                                  );
                                }
                              }),
                            );
                          }
                        }),
                        const SizedBox(width: 12.0),
                        // 输入模式切换按钮或发送按钮
                        Obx(() {
                          if (_isVoiceRecording.value) {
                            return const SizedBox.shrink();
                          }

                          final hasText = _inputText.value.isNotEmpty;

                          return Container(
                            width: 36,
                            height: 36,
                            decoration: BoxDecoration(
                              color: hasText ? Colors.black : Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: IconButton(
                              icon: Icon(
                                hasText ? Icons.arrow_upward : (_isVoiceMode.value ? Icons.keyboard : Icons.mic),
                                color: hasText ? Colors.white : Colors.grey.shade600,
                                size: 20,
                              ),
                              onPressed: () {
                                if (hasText) {
                                  // 发送消息
                                  _handleSendPressed(_inputText.value.trim());
                                  _textController.clear();
                                  _inputText.value = '';
                                  _isTextInputActive.value = false;
                                  _textFocusNode.unfocus();
                                } else {
                                  // 切换输入模式
                                  _isVoiceMode.value = !_isVoiceMode.value;
                                  // 如果切换到文字模式，清空语音相关状态并重置文字输入状态
                                  if (!_isVoiceMode.value) {
                                    _isVoiceRecording.value = false;
                                    _voiceResult.value = '';
                                    _voiceError.value = '';
                                    _isTextInputActive.value = true;
                                    _textFocusNode.requestFocus();
                                  }
                                }
                              },
                            ),
                          );
                        }),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: _isVoiceRecording.value,
                    child: const Positioned.fill(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 16, horizontal: 50),
                        child: AudioVisualizer(
                          isRecording: true,
                          barCount: 50,
                          barWidth: 3,
                          spacing: 1,
                          barColor: Colors.white,
                          backgroundColor: Colors.transparent,
                        ),
                      ),
                    ),
                  ),
                ],
              );
            }),
          ),
        ],
      ),
      onTap: () {
        if (!_isVoiceMode.value) {
          _isTextInputActive.value = true;
          // 延迟聚焦，确保UI更新后再弹出键盘
          Future.delayed(const Duration(milliseconds: 100), () {
            _textFocusNode.requestFocus();
          });
        }
      },
    );
  }
}

class ModelMessage {
  String? role;
  String? content;

  ModelMessage({this.role, this.content});

  // 转换为 JSON 对象的方法
  Map<String, String?> toJson() {
    return {
      'role': role,
      'content': content,
    };
  }
}
