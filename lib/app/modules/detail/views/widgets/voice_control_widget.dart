import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gap/gap.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/utils/image.dart';


import 'package:lsenglish/widgets/wave.dart';
import '../../widgets/record_icon.dart';
import 'base_detail_widget.dart';

/// 语音控制组件 - 使用BaseDetailWidget版本
/// 职责：处理录音、播放、评分等语音相关的UI
class VoiceControlWidget extends BaseSpeechWidget {
  final bool isDark;

  const VoiceControlWidget({
    Key? key,
    this.isDark = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return isLandscape ? _buildLandscapeVoice() : _buildPortraitVoice();
  }

  /// 竖屏语音控制
  Widget _buildPortraitVoice() {
    return Obx(() => Visibility(
          visible: hasSubtitles, // 使用基类访问器
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: _buildVoiceButtons(),
              ),
              Gap(8),
              SizedBox(height: 55, child: _buildVoiceScore()),
            ],
          ),
        ));
  }

  /// 横屏语音控制
  Widget _buildLandscapeVoice() {
    return Obx(() => Visibility(
          visible: hasSubtitles, // 使用基类访问器
          child: Stack(
            children: [
              Center(
                child: Visibility(
                  visible: !isRecording, // 使用基类访问器
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: _buildVoiceButtons(isDark: true, isLandscape: true),
                  ),
                ),
              ),
              Center(child: _buildLandscapeRecordingWidget()),
            ],
          ),
        ));
  }

  /// 语音控制按钮组
  List<Widget> _buildVoiceButtons({bool isDark = false, bool isLandscape = false}) {
    var iconSize = 72.0;
    return [
      GestureDetector(
        onTap: jumpPrevious, // 使用基类方法
        child: Container(
          width: iconSize,
          height: iconSize,
          color: Colors.transparent,
        ),
      ),
      const Gap(24),
      Obx(() => RecordIcon(
            color: Get.theme.primaryColor,
            size: iconSize,
            animationDuration: const Duration(milliseconds: 200),
            isRecording: isRecording, // 使用基类访问器
            onTap: () async {
              await Future.delayed(const Duration(milliseconds: 200));
              if (isRecording) {
                // 使用基类访问器
                stopRecording(); // 使用基类方法
              } else {
                startRecording(); // 使用基类方法
              }
            },
          )),
      const Gap(24),
      GestureDetector(
        onTap: jumpNext, // 使用基类方法
        child: Container(
          width: iconSize,
          height: iconSize,
          color: Colors.transparent,
        ),
      ),
    ];
  }

  /// 语音评分显示
  Widget _buildVoiceScore() {
    return Obx(() {
      // 如果正在录音，显示波形动画
      if (isRecording) {
        // 使用基类访问器
        return WaveformWidget(
          volume: 20,
          barWidth: 2,
          barSpacing: 4,
          animationDuration: 500,
          delayBetweenBars: 100,
          minHeight: 25,
          maxHeight: 35,
          randomizeDuration: 300,
          random: false,
          barColor: Get.theme.primaryColor,
        );
      }

      // 如果没有评分结果，显示空状态
      if (!hasRecording) {
        // 使用基类访问器
        return _buildEmptyScoreState();
      }

      return _buildScoreDisplay();
    });
  }

  /// 空评分状态
  Widget _buildEmptyScoreState() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Container(
        decoration: const BoxDecoration(
          color: Color(0xFFF5F5F5),
          borderRadius: BorderRadius.all(Radius.circular(100)),
        ),
        child: const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Center(
            child: Text(
              "暂无录制",
              style: TextStyle(
                color: Color(0x80000000), // 50% 黑色
                fontSize: 14,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 评分显示
  Widget _buildScoreDisplay() {
    final score = currentScore ?? 0; // 使用基类访问器

    // 根据分数设置颜色和描述
    final scoreConfig = _getScoreConfig(score);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: GestureDetector(
        onTap: playRecord, // 使用基类方法
        child: Container(
          decoration: BoxDecoration(
            color: scoreConfig['backgroundColor'],
            borderRadius: const BorderRadius.all(Radius.circular(100)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Gap(12),
                  ImageLoader(R.voice_wave, color: scoreConfig['textColor']),
                  const Gap(4),
                  Text(
                    score.toString(),
                    style: Get.textTheme.labelLarge?.copyWith(color: scoreConfig['textColor'], fontWeight: FontWeight.bold, letterSpacing: 0),
                  ),
                ],
              ),
              Row(
                children: [
                  Text(
                    scoreConfig['scoreDesc'],
                    style: Get.textTheme.labelLarge?.copyWith(color: scoreConfig['textColor']),
                  ),
                  Gap(4),
                  Icon(Icons.arrow_forward_ios, color: scoreConfig['textColor'], size: 16),
                  Gap(12),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  /// 获取评分配置
  Map<String, dynamic> _getScoreConfig(int score) {
    if (score >= 80) {
      return {
        'backgroundColor': const Color(0xFFDAECDF),
        'textColor': const Color(0xFF009951),
        'scoreDesc': "优秀",
      };
    } else if (score >= 60) {
      return {
        'backgroundColor': const Color(0xFFFFF1C2),
        'textColor': const Color(0xFFB86200),
        'scoreDesc': "良好",
      };
    } else {
      return {
        'backgroundColor': const Color(0xFFFFE2E0),
        'textColor': const Color(0xFFDC3412),
        'scoreDesc': "较差",
      };
    }
  }

  /// 横屏录音动画
  Widget _buildLandscapeRecordingWidget() {
    return Visibility(
      visible: isRecording, // 使用基类访问器
      child: GestureDetector(
        onTap: stopRecording, // 使用基类方法
        child: Container(
          color: Colors.transparent,
          child: IgnorePointer(
            ignoring: true,
            child: WaveformWidget(
              barWidth: 2,
              barSpacing: 4,
              animationDuration: 500,
              delayBetweenBars: 100,
              minHeight: 25,
              maxHeight: 35,
              randomizeDuration: 300,
              random: false,
              barColor: Get.theme.primaryColor,
            ),
          ),
        ),
      ),
    );
  }
}
