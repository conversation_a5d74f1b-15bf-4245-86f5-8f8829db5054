import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'base_detail_widget.dart';

/// 学习进度条组件 - 继承BaseSpeechWidget
/// 职责：显示当前学习进度，包括已完成的句子数量和目标句子数量
class ProgressWidget extends BaseSpeechWidget {
  const ProgressWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) => Row(
        mainAxisSize: MainAxisSize.min, // 添加这个属性
        children: [
          // 进度条
          Flexible(
            child: SizedBox(
              width: 40,
              height: 5,
              child: Obx(() {
                // 从SpeechEvaluationModule获取已录制的句子数量
                final currentSentences = speechModule.recordedSentencesCount.value;
                // 目标句子数量（从PlanDayResp获取）
                final targetSentences = speechModule.currentPlanDay.value?.targetSentences ?? 0;
                final progress = targetSentences > 0 ? currentSentences / targetSentences : 0.0;

                return Stack(
                  children: [
                    // 背景
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                    // 进度条
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      width: 40 * progress, // 使用固定宽度计算
                      decoration: BoxDecoration(
                        color: Get.theme.primaryColor,
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                  ],
                );
              }),
            ),
          ),
          const SizedBox(width: 12),
          // 进度文本
          Obx(() {
            // 从SpeechEvaluationModule获取已录制的句子数量
            final currentSentences = speechModule.recordedSentencesCount.value;
            // 目标句子数量（从PlanDayResp获取）
            final targetSentences = speechModule.currentPlanDay.value?.targetSentences ?? 0;

            return Text(
              '$currentSentences/$targetSentences',
              style: Get.textTheme.titleMedium?.copyWith(fontSize: 15),
            );
          }),
        ],
      ),
    );
  }
}
