import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/utils/image.dart';
import 'package:lsenglish/utils/player_menu.dart';

import 'package:lsenglish/utils/subtitle.dart';
import '../ai_chat_widget.dart';
import '../player_drag_slider.dart';
import 'base_detail_widget.dart';

/// 播放器控制组件 - 使用BaseDetailWidget版本
/// 职责：处理播放器控制菜单、进度条等UI
class PlayerControlWidget extends BasePlayerWidget {
  const PlayerControlWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 进度条
        Obx(() => DualSlider(
              ratio: detailState.seekBarRatio.value, // 使用基类访问器
              onDragEnd: (double ratio) {
                controller.onSliderChange(ratio);
              },
              totalDuration: detailState.totalDuration.value, // 使用基类访问器
            )),
        // 控制菜单
        isLandscape ? _buildLandscapeControl() : _buildPortraitControl(),
      ],
    );
  }

  /// 竖屏控制菜单
  Widget _buildPortraitControl() {
    return SizedBox(
      height: 70,
      width: Get.width,
      child: Obx(() => ReorderableListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: playerMenuItems.length, // 使用基类访问器
            onReorder: (int oldIndex, int newIndex) {
              controller.changeControlMenuSort(newIndex, oldIndex);
            },
            itemBuilder: (context, index) {
              return SizedBox(
                key: ValueKey(playerMenuItems[index].id),
                child: _getControlMenu(playerMenuItems[index].id),
              );
            },
          )),
    );
  }

  /// 横屏控制菜单
  Widget _buildLandscapeControl() {
    return SizedBox(
      width: 70,
      height: Get.height,
      child: Obx(() => ReorderableListView.builder(
            scrollDirection: Axis.vertical,
            itemCount: playerMenuItems.length, // 使用基类访问器
            onReorder: (int oldIndex, int newIndex) {
              controller.changeControlMenuSort(newIndex, oldIndex);
            },
            itemBuilder: (context, index) {
              return Container(
                key: ValueKey(playerMenuItems[index].id),
                child: _getControlMenu(playerMenuItems[index].id, isLandscape: true),
              );
            },
          )),
    );
  }

  /// 获取控制菜单项
  Widget _getControlMenu(int id, {bool isLandscape = false}) {
    Widget buildMenu(String image, {bool useSelect = false, VoidCallback? onTap}) {
      final color = (isLandscape ? Colors.white.withValues(alpha: 0.7) : Get.theme.colorScheme.onSecondary);
      return GestureDetector(
        onTap: onTap,
        child: Container(
          color: Colors.transparent,
          child: Padding(
            padding: isLandscape
                ? EdgeInsets.symmetric(horizontal: 10, vertical: 19)
                : EdgeInsets.only(bottom: 20, left: 19, right: 19),
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return FadeTransition(
                  opacity: animation,
                  child: child,
                );
              },
              child: Container(
                key: ValueKey('$image-$useSelect'),
                child: ImageLoader(
                  image,
                  size: 24,
                  color: useSelect ? Get.theme.primaryColor : color,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
        ),
      );
    }

    return _buildMenuByType(id, buildMenu, isLandscape);
  }

  /// 根据类型构建菜单项
  Widget _buildMenuByType(int id, Widget Function(String, {bool useSelect, VoidCallback? onTap}) buildMenu, bool isLandscape) {
    switch (id) {
      case PlayerMenuId.note:
        return buildMenu(R.edit, onTap: () => showNoteDialog()); // 使用基类方法

      case PlayerMenuId.ai:
        return buildMenu(R.ai, onTap: () {
          final subtitle = currentSubtitle; // 使用基类访问器
          if (subtitle != null) {
            Get.to(AiChatWidget(subtitle: subtitle));
          }
        });

      case PlayerMenuId.collect:
        return Obx(() {
          final shouldShowSelected = !isLandscape && hasSubtitles && isCurrentSubtitleCollected; // 使用基类访问器

          return buildMenu(
            hasSubtitles && isCurrentSubtitleCollected // 使用基类访问器
                ? (isLandscape ? R.heart_select : R.heart)
                : (isLandscape ? R.heart : R.heart),
            useSelect: shouldShowSelected,
            onTap: toggleCollect, // 使用基类方法
          );
        });

      case PlayerMenuId.rate:
        return Obx(() => buildMenu(
              videoKit.currentSpeed.value == 1.0 ? R.speed : R.speed_half, // 使用基类访问器
              useSelect: videoKit.currentSpeed.value == 1.0,
              onTap: switchSpeed, // 使用基类方法
            ));

      case PlayerMenuId.eye:
        return Obx(() => buildMenu(
              isSubtitlePlaceholderVisible ? R.eye_close : R.eye_open, // 使用基类访问器
              useSelect: isSubtitlePlaceholderVisible,
              onTap: toggleSubtitlePlaceholder, // 使用基类方法
            ));

      case PlayerMenuId.translate:
        return Obx(() => buildMenu(
              videoKit.currentSubtitleMode.value == SubtitleMode.target.index // 使用基类访问器
                  ? R.translate_target
                  : (videoKit.currentSubtitleMode.value == SubtitleMode.native.index ? R.translate_native : R.translate),
              useSelect: videoKit.currentSubtitleMode.value == SubtitleMode.target.index,
              onTap: () => videoKit.switchSubtitleMode(), // 使用基类访问器
            ));

      case PlayerMenuId.editSubtitle:
        return buildMenu(R.edit_subtitle, onTap: () => subtitleModule.editSubtitle()); // 使用基类访问器

      case PlayerMenuId.skip:
        return Obx(() => buildMenu(videoKit.onlyPlayLines.value ? R.only_lines_active : R.only_lines_off, // 使用基类访问器
            useSelect: videoKit.onlyPlayLines.value,
            onTap: () => videoKit.switchOnlyPlayLines())); // 使用基类访问器

      case PlayerMenuId.more:
        return buildMenu(R.setting, onTap: () => controller.goMoreSetting());

      default:
        return Obx(() => buildMenu(R.setting));
    }
  }
}
