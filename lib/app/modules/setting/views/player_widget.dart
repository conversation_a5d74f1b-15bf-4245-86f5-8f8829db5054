import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/config/config.dart';


import 'setting_warp.dart';

class PlayerSettingWidget extends StatelessWidget {
  const PlayerSettingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              "播放",
              style: Get.textTheme.bodyLarge
                  ?.copyWith(color: Get.theme.primaryColor),
            ),
          ),
          Gap(12),
          SettingItem(
              child: Column(
            children: [
              Row(
                children: [
                  Text(
                    "录制结束后显示字幕",
                    style: Get.textTheme.titleSmall,
                  ),
                  const Spacer(),
                  Obx(() => CupertinoSwitch(
                        activeColor: Get.theme.primaryColor,
                        value: Config().showSubtitleWhenRecordEnd.value,
                        onChanged: (bool value) {
                          Config().setShowSubtitleWhenRecordEnd();
                        },
                      ))
                ],
              ),
              const Divider(),
              Row(
                children: [
                  Text(
                    "录制结束后自动播放录制声音",
                    style: Get.textTheme.titleSmall,
                  ),
                  const Spacer(),
                  Obx(() => CupertinoSwitch(
                        activeColor: Get.theme.primaryColor,
                        value: Config().autoPlayRecordWhenRecordEnd.value,
                        onChanged: (bool value) {
                          Config().setAutoPlayRecordWhenRecordEnd();
                        },
                      ))
                ],
              ),
              const Divider(),
              Row(
                children: [
                  Text(
                    "自动开始录制",
                    style: Get.textTheme.titleSmall,
                  ),
                  const Spacer(),
                  Obx(() => CupertinoSwitch(
                        activeColor: Get.theme.primaryColor,
                        value: Config().autoRecord.value,
                        onChanged: (bool value) {
                          Config().setAutoRecord();
                        },
                      ))
                ],
              ),
              const Divider(),
              Row(
                children: [
                  Text(
                    "自动停止录制",
                    style: Get.textTheme.titleSmall,
                  ),
                  const Spacer(),
                  Obx(() => CupertinoSwitch(
                        activeColor: Get.theme.primaryColor,
                        value: Config().autoStopRecord.value,
                        onChanged: (bool value) {
                          Config().setAutoStopRecord();
                        },
                      ))
                ],
              ),
              const Divider(),
              Row(
                children: [
                  Text(
                    "单句循环播放",
                    style: Get.textTheme.titleSmall,
                  ),
                  const Spacer(),
                  Obx(() => CupertinoSwitch(
                        activeColor: Get.theme.primaryColor,
                        value: Config().openSingleRepeat.value,
                        onChanged: (bool value) {
                          Config().setOpenSingleRepeat();
                        },
                      ))
                ],
              ),
            ],
          )),
        ],
      ),
    );
  }
}
