import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish/utils/image.dart';

import '../controllers/resourcelib_controller.dart';

class ResourcelibView extends GetView<ResourcelibController> {
  const ResourcelibView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '资料库',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 34,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Gap(16),
            Padding(padding: EdgeInsets.only(left: 16), child: Text("精选推荐", style: TextStyle(fontSize: 24, fontWeight: FontWeight.w600))),
            Gap(16),
            SizedBox(
              height: 163,
              child: Obx(() => ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: controller.recommendList.length,
                    itemBuilder: (BuildContext context, int index) {
                      return GestureDetector(
                        onTap: () => controller.onRecommendClick(index),
                        child: Padding(
                          padding: EdgeInsets.only(left: index == 0 ? 16 : 8, right: 8),
                          child: AspectRatio(
                            aspectRatio: 330 / 160,
                            child: Stack(
                              children: [
                                Positioned.fill(
                                    child: Container(
                                        clipBehavior: Clip.antiAlias,
                                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(16)),
                                        child: ImageLoader(controller.recommendList[index].cover ?? "")))
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  )),
            ),
            Gap(16),
            Obx(
              () => ListView.builder(
                shrinkWrap: true,
                scrollDirection: Axis.vertical,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: controller.categoryTypeList.length,
                itemBuilder: (BuildContext context, int index) {
                  return Theme(
                    data: Theme.of(context).copyWith(tabBarTheme: const TabBarThemeData(labelPadding: EdgeInsets.zero)),
                    child: TabBar(
                      tabAlignment: TabAlignment.start,
                      labelPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                      padding: EdgeInsets.symmetric(vertical: 8),
                      isScrollable: true,
                      controller: controller.tabControllers[index],
                      overlayColor: WidgetStateProperty.all(Colors.transparent),
                      tabs: controller.categoryTypeList[index].categories!.map((label) => Text(label.name ?? "")).toList(),
                      labelColor: Colors.black,
                      labelStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      indicatorColor: Colors.black,
                      unselectedLabelStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                      unselectedLabelColor: const Color(0xFF625B71),
                      dividerColor: Colors.transparent,
                      indicatorSize: TabBarIndicatorSize.label,
                    ),
                  );
                },
              ),
            ),
            Gap(20),
            Obx(() => ListView.builder(
                  scrollDirection: Axis.vertical,
                  itemCount: controller.resourceList.length,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (BuildContext context, int index) {
                    return GestureDetector(
                      onTap: () {
                        controller.onItemClick(index);
                      },
                      child: Container(
                        color: Colors.transparent,
                        child: Padding(
                          padding: EdgeInsets.only(top: index == 0 ? 0 : 8, bottom: 8, left: 16, right: 16),
                          child: SizedBox(
                            height: 100,
                            child: Row(
                              children: [
                                Container(
                                    clipBehavior: Clip.antiAlias,
                                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(16)),
                                    child: AspectRatio(
                                      aspectRatio: 1,
                                      child: ImageLoader(controller.resourceList[index].cover ?? "", size: 100),
                                    )),
                                Gap(8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        controller.resourceList[index].name ?? "--",
                                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      Gap(8),
                                      Text(controller.resourceList[index].contentType == 1 ? "剧集" : "视频"),
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ))
          ],
        ),
      ),
    );
  }
}
