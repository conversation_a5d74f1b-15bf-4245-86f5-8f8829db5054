import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../controllers/login_controller.dart';

class LoginView extends GetView<LoginController> {
  const LoginView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Column(
        children: [
          FilledButton(
            onPressed: () {
              controller.loginByPassword();
            },
            child: const Text("测试登录,请不要点击"),
          ),
          FilledButton(
            onPressed: () {
              controller.loginTing();
            },
            child: const Text("线上ting账号登录,请不要点击"),
          ),
          Gap(200),
          Center(
            child: SizedB<PERSON>(
              width: 200,
              child: SignInWithAppleButton(
                text: "Apple登录",
                onPressed: () {
                  controller.appleLogin();
                },
              ),
            ),
          )
        ],
      ),
    );
  }
}
