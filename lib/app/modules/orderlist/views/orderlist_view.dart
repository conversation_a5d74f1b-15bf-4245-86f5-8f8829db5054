import 'package:flutter/material.dart';

import 'package:get/get.dart';


import '../controllers/orderlist_controller.dart';

class OrderlistView extends GetView<OrderlistController> {
  const OrderlistView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('OrderlistView'),
        centerTitle: true,
      ),
      body: Obx(() => ListView.builder(
            itemCount: controller.orderList.length,
            itemBuilder: (BuildContext context, int index) {
              return Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("订单编号:${controller.orderList[index].orderNo ?? "--"}"),
                    Text("价格:${controller.orderList[index].amount} ${controller.orderList[index].currency}"),
                    Text("状态:${controller.orderList[index].status}"),
                    Text("产品名字:${controller.orderList[index].productName}"),
                    Text("支付时间:${controller.orderList[index].paidTimestamp}"),
                  ],
                ),
              );
            },
          )),
    );
  }
}
