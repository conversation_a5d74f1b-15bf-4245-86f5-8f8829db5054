import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:media_kit/media_kit.dart';
import 'package:audio_service/audio_service.dart';
import 'player.dart';

class MediaPlayer extends BasePlayer {
  var _player = Player();
  final List<StreamSubscription?> subscriptions = [];
  var currentVolume = 50.0;
  var isPlayerInitialized = false;
  @override
  void init() async {
    super.init();
    _player = Player();
    if (_player.platform is NativePlayer) {
      await (_player.platform as dynamic).setProperty(
        'force-seekable',
        'yes',
      );
    }
    await _player.setSubtitleTrack(SubtitleTrack.no());

    // 初始化 AudioService
    await initializeAudioService();
    isPlayerInitialized = true;
  }

  @override
  Future<Uint8List?> screenshot() async {
    return await _player.screenshot();
  }

  @override
  Future open(String path) async {
    await super.open(path);
    await _player.open(Media(path), play: false);
    onVideoLoadFinish();

    currentVolume = _player.state.volume;
    subscriptions.clear();
    subscriptions.addAll([
      _player.stream.position.listen((duration) {
        onPosition(duration);
      }),
      _player.stream.playing.listen((event) {
        playing.value = event;
      }),
      _player.stream.volume.listen((onData) {
        if (onData > 0) {
          currentVolume = onData;
        }
      }),
      _player.stream.audioParams.listen((event) {
        if (event.format != null) {}
      }),
    ]);
  }

  @override
  Future<void> seek(Duration duration) async {
    await _player.seek(duration);
  }

  @override
  Future<void> switchSpeed() async {
    if (currentSpeed.value == 1.0) {
      currentSpeed.value = 0.5;
    } else {
      currentSpeed.value = 1.0;
    }
    await _player.setRate(currentSpeed.value);
  }

  @override
  bool isPlaying() {
    return _player.state.playing;
  }

  @override
  Future<void> pause() async {
    await super.pause();
    await _player.pause();
  }

  @override
  Future<void> play() async {
    await super.play();
    await _player.play();
  }

  @override
  void stop() {
    _player.stop();
  }

  @override
  void playOrPause() {
    super.playOrPause();
    _player.playOrPause();
  }

  @override
  Future<void> destory() async {
    super.destory();
    for (final subscription in subscriptions) {
      subscription?.cancel();
    }
    subscriptions.clear();
    _player.dispose();

    // 清理 AudioService
    try {
      await AudioService.stop();
    } catch (e) {
      // 忽略清理错误
    }
  }

  Player get getPlayer => _player;

  @override
  Future<void> mute() async {
    await _player.setVolume(0);
  }

  @override
  Future<void> unmute() async {
    await _player.setVolume(currentVolume);
  }

  @override
  bool isInitialized() {
    return isPlayerInitialized;
  }
}
