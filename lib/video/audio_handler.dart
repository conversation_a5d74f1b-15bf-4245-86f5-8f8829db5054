import 'dart:async';
import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';
import 'package:flutter/foundation.dart';
import 'package:lsenglish/model/local_detail_resp/resource_detail_resp.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/video/media_player.dart';

class MyAudioHandler extends BaseAudioHandler with SeekHandler {
  late StreamController<PlaybackState> streamController;
  final List<StreamSubscription> _subscriptions = [];
  Duration _lastReportedPosition = Duration.zero;
  DateTime _lastPositionUpdate = DateTime.now();
  // 视频播放器相关函数
  Function? _videoPlay;
  Function? _videoPause;
  Function? _videoSeek;
  Function? _videoStop;
  Function? _videoFastForward;
  Function? _videoRewind;
  var _item = MediaItem(
    id: 'SEEDTU',
    album: "SEEDTU",
    title: "SEEDTU",
    artist: "SEEDTU",
    duration: const Duration(minutes: 5), // 设置一个合理的时长
    artUri: Uri.parse('https://gips3.baidu.com/it/u=3886271102,3123389489&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960'),
  );

  MyAudioHandler() {
    logger("MyAudioHandler constructor");
    mediaItem.add(_item);
    logger("MyAudioHandler: 初始化完成，MediaItem 已设置");
  }

  void setMediaItem(ResourceDetailResp resourceDetailResp) {
    _item = MediaItem(
      id: resourceDetailResp.resourceId ?? "",
      album: resourceDetailResp.nativeLangResourceRelation?.title ?? "",
      title: resourceDetailResp.nativeLangResourceRelation?.title ?? "",
      artist: "SEEDTU",
      duration: Duration(milliseconds: resourceDetailResp.duration ?? 0),
      artUri: Uri.parse(resourceDetailResp.cover ?? ""),
    );
    mediaItem.add(_item);
  }

  /// 设置视频播放器函数
  void setVideoFunctions(Function play, Function pause, Function seek, Function fastForward, Function rewind, Function stop) {
    _videoPlay = play;
    _videoPause = pause;
    _videoSeek = seek;
    _videoFastForward = fastForward;
    _videoRewind = rewind;
    _videoStop = stop;
    logger("MyAudioHandler: 视频播放器函数已设置");
    logger("AudioPlayerHandler: 设置视频函数，同时设置 MediaItem");
    mediaItem.add(_item);
  }

  // 重新激活音频会话的方法
  Future<void> reactivateAudioSession() async {
    try {
      final session = await AudioSession.instance;
      await session.configure(const AudioSessionConfiguration.music());
      logger("AudioPlayerHandler: 音频会话重新激活成功");

      // 重新设置 MediaItem
      mediaItem.add(_item);
    } catch (e) {
      logger("AudioPlayerHandler: 音频会话重新激活失败: $e");
    }
  }

  // 播放控制方法
  @override
  Future<void> play() async {
    logger("MyAudioHandler: play");
    // 确保 MediaItem 被设置
    mediaItem.add(_item);

    // 尝试重新激活音频会话
    try {
      final session = await AudioSession.instance;
      await session.configure(const AudioSessionConfiguration.music());
      logger("MyAudioHandler: 音频会话重新激活成功");
    } catch (e) {
      logger("AudioPlayerHandler: 音频会话重新激活失败: $e");
    }

    _videoPlay?.call();

    logger("AudioPlayerHandler: 播放状态已更新，应该显示在控制中心");
  }

  @override
  Future<void> pause() async {
    logger("MyAudioHandler: pause");
    _videoPause?.call();
  }

  @override
  Future<void> seek(Duration position) async {
    logger("MyAudioHandler: seek to ${position.inMilliseconds}ms");
    _videoSeek?.call(position);
  }

  @override
  Future<void> stop() async {
    logger("MyAudioHandler: stop");
    _videoStop?.call();
  }

  @override
  Future<void> rewind() async {
    logger("MyAudioHandler: rewind");
    _videoRewind?.call();
  }

  @override
  Future<void> fastForward() async {
    logger("MyAudioHandler: fastForward");
    _videoFastForward?.call();
  }

  /// 初始化流控制器
  void initializeStreamController(MediaPlayer? videoPlayerController) {
    if (videoPlayerController == null) return;
    bool isPlaying() => videoPlayerController.isPlaying();

    AudioProcessingState processingState() {
      // 添加详细的处理状态
      switch (videoPlayerController.getPlayer.state.buffering) {
        case true:
          return AudioProcessingState.buffering;
        case false:
          return videoPlayerController.getPlayer.state.completed ? AudioProcessingState.completed : AudioProcessingState.ready;
      }
    }

    Duration bufferedPosition() {
      return videoPlayerController.getPlayer.state.buffer;
    }

    void updatePlaybackState() {
      final state = PlaybackState(
        controls: [
          MediaControl.rewind,
          if (isPlaying()) MediaControl.pause else MediaControl.play,
          MediaControl.stop,
          MediaControl.fastForward,
        ],
        systemActions: const {
          MediaAction.seek,
          MediaAction.seekForward,
          MediaAction.seekBackward,
        },
        androidCompactActionIndices: const [0, 1, 3],
        processingState: processingState(),
        playing: isPlaying(),
        updatePosition: videoPlayerController.getPlayer.state.position,
        bufferedPosition: bufferedPosition(),
        speed: videoPlayerController.getPlayer.state.rate,
      );

      streamController.add(state);
    }

    // 清理之前的订阅
    for (var sub in _subscriptions) {
      sub.cancel();
    }
    _subscriptions.clear();

    // 创建新的流控制器
    streamController = StreamController<PlaybackState>();

    // 监听所有相关状态变化
    _subscriptions.addAll([
      videoPlayerController.getPlayer.stream.playing.listen((_) => updatePlaybackState()),
      // videoPlayerController.getPlayer.stream.position.listen((_) => _updatePlaybackState()),
      videoPlayerController.getPlayer.stream.position.listen((position) {
        if (defaultTargetPlatform == TargetPlatform.android) {
          // Android 平台应用节流逻辑
          final now = DateTime.now();
          final timeSinceLastUpdate = now.difference(_lastPositionUpdate);
          final positionDiff = (position - _lastReportedPosition).abs();

          // 只有当位置变化超过2秒，或者距离上次更新超过1秒时才更新
          if (positionDiff.inSeconds >= 2 || timeSinceLastUpdate.inSeconds >= 1) {
            logger("AudioPlayerHandler: position 变化 - ${position.inSeconds}s (Android 节流后)");
            _lastReportedPosition = position;
            _lastPositionUpdate = now;
            updatePlaybackState();
          }
        } else {
          // iOS 平台直接更新，保持原有体验
          logger("AudioPlayerHandler: position 变化 - ${position.inSeconds}s (iOS 直接更新)");
          updatePlaybackState();
        }
      }),
      videoPlayerController.getPlayer.stream.buffering.listen((_) => updatePlaybackState()),
      videoPlayerController.getPlayer.stream.completed.listen((_) => updatePlaybackState()),
      // videoPlayerController.getPlayer.stream.buffer.listen((_) => _updatePlaybackState()),
      // 监听视频时长变化，更新 MediaItem
      videoPlayerController.getPlayer.stream.duration.listen((duration) {
        if (duration.inMilliseconds > 0) {
          logger("MyAudioHandler: 视频加载完成，更新 MediaItem 时长 - ${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}");
          _item = MediaItem(
            id: _item.id,
            album: _item.album,
            title: _item.title,
            artist: _item.artist,
            duration: duration,
            artUri: _item.artUri,
          );
          mediaItem.add(_item);
        }
      }),
    ]);

    // 关键步骤：将 streamController.stream 绑定到 playbackState
    playbackState.addStream(streamController.stream);
    // 立即发送初始状态
    updatePlaybackState();

    logger("MyAudioHandler: 流控制器初始化完成");
  }
}
