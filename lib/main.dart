// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_crashlytics/firebase_crashlytics.dart';

import 'package:audio_service/audio_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_storage/get_storage.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/utils/file.dart';
import 'package:lsenglish/video/audio_handler.dart';
import 'package:lsenglish/video/player.dart';
import 'net/net.dart';
import 'my_app.dart';

late MyAudioHandler audioHandler;
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await AudioService.init(
    builder: () {
      audioHandler = MyAudioHandler();
      return audioHandler;
    },
    config: const AudioServiceConfig(
      androidNotificationChannelId: 'com.mikaelzero.lsenglish.channel.audio',
      androidNotificationChannelName: 'SEEDTU 视频播放',
      androidNotificationOngoing: true,
      androidStopForegroundOnPause: true,
      // iOS 特定配置
      fastForwardInterval: Duration(seconds: 10),
      rewindInterval: Duration(seconds: 10),
    ),
  );
  // Config().isDev = true;
  if (kReleaseMode) {
    Net.configureDio(baseUrl: "https://test.seedtu.com/");
  } else if (Config().isDev) {
    Net.configureDio(baseUrl: "http://192.168.1.13:3000/");
    // Net.configureDio(baseUrl: "https://test.seedtu.com/");
  } else {
    Net.configureDio(baseUrl: "https://test.seedtu.com");
  }
  await GetStorage.init();
  // if (kReleaseMode) {
  //   await Firebase.initializeApp(
  //     options: DefaultFirebaseOptions.currentPlatform,
  //   );
  //   FlutterError.onError = (errorDetails) {
  //     FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  //   };
  //   PlatformDispatcher.instance.onError = (error, stack) {
  //     FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
  //     return true;
  //   };
  // }

  await FileUtils().ensureDirectory();
  Config().init();
  await IPlayer.ensureInitialized();
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent, // 状态栏透明
      systemNavigationBarColor: Colors.transparent, // 导航栏透明
      systemNavigationBarIconBrightness: Brightness.dark, // 导航栏图标暗色
      statusBarIconBrightness: Brightness.dark, // 状态栏图标暗色
    ),
  );
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.edgeToEdge,
    overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
  ).then((_) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]).then((_) {
      runApp(const MyApp());
    });
  });
}

// import 'package:flutter/material.dart';
// import 'package:receive_sharing_intent/receive_sharing_intent.dart';

// void main() => runApp(MyApp());

// class MyApp extends StatefulWidget {
//   @override
//   _MyAppState createState() => _MyAppState();
// }

// class _MyAppState extends State<MyApp> {
//   var files = "等待接收";
//   @override
//   void initState() {
//     super.initState();

//     // 监听共享来的视频文件
//     receive();
//   }

//   void receive() {
//     ReceiveSharingIntent.instance.getMediaStream().listen((List<SharedMediaFile> value) {
//       if (value.isNotEmpty) {
//         // 获取视频文件路径
//         String path = value.first.path;
//         setState(() {
//           files = path;
//         });
//       }
//     }, onError: (err) {
//       print("获取共享内容出错: $err");
//     });

//     // 处理应用关闭时收到的意图
//     ReceiveSharingIntent.instance.getInitialMedia().then((List<SharedMediaFile> value) {
//       print(value);
//       if (value.isNotEmpty) {
//         String path = value.first.path;
//         setState(() {
//           files = path;
//         });
//       }
//     }, onError: (err) {
//       print("111获取共享内容出错: $err");
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return MaterialApp(
//       home: Scaffold(
//         body: Center(
//             child: GestureDetector(
//                 onTap: () {
//                   receive();
//                 },
//                 child: Text(files))),
//       ),
//     );
//   }
// }
